package com.haoxue.libcommon.ui.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.WindowManager
import com.haoxue.libcommon.R
import com.haoxue.libcommon.utils.PixelUtils

/**
 * 页面提示框
 */
@Suppress("RECEIVER_NULLABILITY_MISMATCH_BASED_ON_JAVA_ANNOTATIONS")
abstract class CommonBaseDialog(var mContext: Context) : Dialog(mContext, R.style.common_Dialog) {

    init {
        setContentView(setContentLayout())
        initData()
    }

    abstract fun initData()

    abstract fun setContentLayout(): Int


    fun initWidth() {
        if (PixelUtils.isPrortrait) setWidth(330 / 375f, 0f) else setWidth(42 / 100f, 0f)
    }

    /**
     * 设置宽高比
     *
     * @param width
     */
    fun setWidthAndHeight(width: Float, b: Float) {
        setWidth(width, b)
    }

    protected fun initWidth2() {
        setWidth(5 / 6f, 0f)
    }

    private fun setWidth(a: Float, b: Float) {
        val dm = DisplayMetrics()
        val wm = mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        wm.defaultDisplay.getMetrics(dm)
        val width = dm.widthPixels
        val height = dm.heightPixels
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (a == 1f && b == 1f) {
            window?.setGravity(Gravity.TOP)
        } else {
            window?.setGravity(Gravity.CENTER)
        }
        val lp = window?.attributes!!

        if (a != 0f)
            lp.width = (width * a).toInt()
        if (b != 0f) {
            if (a == 1f && b == 1f) {
                lp.height = height - PixelUtils.getStateBarHeight()
            } else {
                lp.height = (width.toFloat() * a * b).toInt()
            }
        }
        window?.setLayout(lp.width, lp.height)
        window?.setWindowAnimations(R.style.common_dialogAnimation)
    }
}