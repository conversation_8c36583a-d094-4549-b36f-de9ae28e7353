package com.haoxue.libcommon.ui.dialog

import android.content.Context
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.haoxue.libcommon.R
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import com.haoxue.libcommon.singleClick


/**
 * common alert dialog
 */
class CommonAlertDialog(context: Context) : CommonBaseDialog(context) {

    private var mTvTitle: TextView? = null
    private var mTvMsg: TextView? = null

    //nature 优先级高
    private var mBtnNature: TextView? = null
    private var mBtnPos: TextView? = null
    private var mBtnNeg: TextView? = null
    private var mBtnsContainer: LinearLayout? = null

    override fun initData() {
        mTvTitle = findViewById(R.id.tvTitle)
        mTvMsg = findViewById(R.id.tvMsg)
        mBtnNature = findViewById(R.id.btnNature)
        mBtnPos = findViewById(R.id.btnPos)
        mBtnNeg = findViewById(R.id.btnNeg)
        mBtnsContainer = findViewById(R.id.btnsContainer)
    }

    override fun show() {
        super.show()
        initWidth()
    }

    override fun setContentLayout() = R.layout.common_alert_dialog


    class Builder(private val mContext: Context) {
        private val mParams: AlertParams = AlertParams()

        fun setTitle(title: String?): Builder {
            mParams.mTitle = title
            return this
        }

        fun setTitle(@StringRes titleRes: Int): Builder {
            mParams.mTitle = mContext.getString(titleRes)
            return this
        }

        fun setClose(boolean: Boolean): Builder {
            mParams.closeShow = boolean
            return this
        }

        fun setMessage(message: String): Builder {
            mParams.mMsg = message
            return this
        }

        fun setMessageGravityCenter(): Builder {
            mParams.gravity = Gravity.CENTER
            return this
        }

        fun setMessageTextColor(messageTextColor: Int): Builder {
            mParams.messageTextColor = messageTextColor
            return this
        }

        fun setMessage(@StringRes messageRes: Int): Builder {
            mParams.mMsg = mContext.getString(messageRes)
            return this
        }

        fun setPositiveButton(onPositiveClickListener: ViewOnclickListener?): Builder {
            mParams.mPosBtnText = "确定"
            mParams.mOnPosClickListener = onPositiveClickListener
            return this
        }


        fun setPositiveButton(
            posText: String,
            onPositiveClickListener: ViewOnclickListener?
        ): Builder {
            mParams.mPosBtnText = posText
            mParams.mOnPosClickListener = onPositiveClickListener
            return this
        }

        fun setPositiveButton(
            @StringRes posTextRes: Int,
            onPositiveClickListener: ViewOnclickListener?
        ): Builder {
            mParams.mPosBtnText = mContext.getString(posTextRes)
            mParams.mOnPosClickListener = onPositiveClickListener
            return this
        }

        fun setPositivtTextColor(@ColorRes posTextColor: Int): Builder {
            mParams.mPosTextColor = posTextColor
            return this
        }

        @JvmOverloads
        fun setNegativeButton(
            negText: String = "取消",
            onNegativeClickListener: ViewOnclickListener? = null
        ): Builder {
            mParams.mNegBtnText = negText
            mParams.mOnNegClickListener = onNegativeClickListener
            return this
        }

        fun setNegativeButton(
            @StringRes negTextRes: Int,
            onNegativeClickListener: ViewOnclickListener?
        ): Builder {
            mParams.mNegBtnText = mContext.getString(negTextRes)
            mParams.mOnNegClickListener = onNegativeClickListener
            return this
        }

        fun setNegativeTextColor(@ColorRes negTextColor: Int): Builder {
            mParams.mNegTextColor = negTextColor
            return this
        }

        @JvmOverloads
        fun setNatureButton(
            natureText: String = "确认",
            onNatureClickListener: ViewOnclickListener? = null
        ): Builder {
            mParams.mNatureText = natureText
            mParams.mOnNatureClickListener = onNatureClickListener
            return this
        }

        fun setNatureButton(
            @StringRes natureTextRes: Int,
            onNatureClickListener: ViewOnclickListener?
        ): Builder {
            mParams.mNatureText = mContext.getString(natureTextRes)
            mParams.mOnNatureClickListener = onNatureClickListener
            return this
        }

        fun setnatureTextColor(@ColorRes natureTextColor: Int): Builder {
            mParams.mNatureTextColor = natureTextColor
            return this
        }

        fun setCancelable(cancelable: Boolean): Builder {
            mParams.mCancelable = cancelable
            return this
        }

        fun setOutSideCancelable(outSideCancelable: Boolean): Builder {
            mParams.mOutSideCancelable = outSideCancelable
            return this
        }

        fun create(): CommonAlertDialog {
            val dialog = CommonAlertDialog(mContext)
            mParams.apply(dialog)
            return dialog
        }

        fun show() {
            val dialog = create()
            dialog.show()
        }
    }

    private class AlertParams {
        internal var mTitle: String? = null
        internal var mMsg: String? = null
        internal var gravity = Gravity.CENTER
        internal var mPosBtnText: String? = null
        internal var mNegBtnText: String? = null
        internal var mNatureText: String? = null

        @ColorRes
        internal var mPosTextColor: Int = 0

        @ColorRes
        internal var messageTextColor: Int = 0

        @ColorRes
        internal var mNegTextColor: Int = 0

        @ColorRes
        internal var mNatureTextColor: Int = 0

        internal var mOnPosClickListener: ViewOnclickListener? = null
        internal var mOnNegClickListener: ViewOnclickListener? = null
        internal var mOnNatureClickListener: ViewOnclickListener? = null
        internal var mCancelable: Boolean = true
        internal var mOutSideCancelable: Boolean = true
        internal var closeShow: Boolean = false


        internal fun apply(dialog: CommonAlertDialog?) {
            if (dialog == null) return

            if (!TextUtils.isEmpty(mTitle)) {
                dialog.mTvTitle!!.text = mTitle
            } else {
                dialog.mTvTitle!!.visibility = View.GONE
            }

            if (!TextUtils.isEmpty(mMsg)) {
                dialog.mTvMsg!!.text = mMsg
                dialog.mTvMsg!!.gravity = gravity
            } else {
                dialog.mTvMsg!!.visibility = View.GONE
            }

            if (messageTextColor != 0) {
                dialog.mTvMsg!!.setTextColor(
                    ContextCompat.getColor(
                        dialog.context,
                        messageTextColor
                    )
                )
            }

            dialog.setCancelable(mCancelable)
            dialog.setCanceledOnTouchOutside(mOutSideCancelable)

            if (!TextUtils.isEmpty(mNatureText)) {
                dialog.mBtnNature!!.visibility = View.VISIBLE
                dialog.mBtnsContainer!!.visibility = View.GONE
                dialog.mBtnNature!!.text = mNatureText
                if (mNatureTextColor != 0) {
                    dialog.mBtnNature!!.setTextColor(
                        ContextCompat.getColor(
                            dialog.context,
                            mNatureTextColor
                        )
                    )
                }
                dialog.mBtnNature!!.singleClick {
                    if (mOnNatureClickListener != null) {
                        mOnNatureClickListener!!.onClick(it, dialog)
                    }
                    dialog.dismiss()
                }
                return
            } else {
                dialog.mBtnNature!!.visibility = View.GONE
                dialog.mBtnsContainer!!.visibility = View.VISIBLE
            }

            if (!TextUtils.isEmpty(mPosBtnText)) {
                dialog.mBtnPos!!.text = mPosBtnText
                if (mPosTextColor != 0) {
                    dialog.mBtnPos!!.setTextColor(
                        ContextCompat.getColor(
                            dialog.context,
                            mPosTextColor
                        )
                    )
                }
                dialog.mBtnPos!!.singleClick {
                    if (mOnPosClickListener != null) {
                        mOnPosClickListener!!.onClick(it, dialog)
                    }
                    dialog.dismiss()
                }
            }

            if (!TextUtils.isEmpty(mNegBtnText)) {
                dialog.mBtnNeg!!.text = mNegBtnText
                if (mNegTextColor != 0) {
                    dialog.mBtnNeg!!.setTextColor(
                        ContextCompat.getColor(
                            dialog.context,
                            mNegTextColor
                        )
                    )
                }
                dialog.mBtnNeg!!.singleClick {
                    if (mOnNegClickListener != null) {
                        mOnNegClickListener!!.onClick(it, dialog)
                    }
                    dialog.dismiss()
                }
            }
        }
    }

    interface ViewOnclickListener {
        fun onClick(view: View, dialog: CommonAlertDialog)
    }

}
