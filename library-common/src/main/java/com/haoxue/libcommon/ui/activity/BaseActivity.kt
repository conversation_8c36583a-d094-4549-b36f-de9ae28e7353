package com.haoxue.libcommon.ui.activity

import android.app.Activity
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.alguojian.mylibrary.StatusLayout
import com.haoxue.libcommon.state.StatusHelperListener
import com.haoxue.libcommon.utils.AppManager
import java.lang.ref.WeakReference

abstract class BaseActivity<T : ViewDataBinding>(private val contentLayoutId: Int = 0) : AppCompatActivity(), StatusHelperListener {

    private var mWeakReference: WeakReference<Activity>? = null
    lateinit var mContext: Context
    protected lateinit var mBinding: T

    private lateinit var statusHelper: StatusLayout.StatusHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        enableEdgeToEdge()

//        window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
//        hideStatusBar()
        mContext = this
        mWeakReference = WeakReference(this)
        onCreateInit()

        AppManager.addActivity(mWeakReference)
        if (contentLayoutId == 0) return
        mBinding = DataBindingUtil.setContentView(this, contentLayoutId) as T
        getSavedInstanceState(savedInstanceState)
        statusHelper = StatusLayout.attachView(this).onRetryClick { requestCommonData() }
        initCommonData()
        initCommonListener()
        requestCommonData()
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
//            hideStatusBar()
        }
    }

    private fun hideStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.insetsController?.let { controller ->
                controller.hide(WindowInsets.Type.statusBars())
                controller.systemBarsBehavior =
                    WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val decorView = window.decorView
            val uiOptions = (View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
            decorView.systemUiVisibility = uiOptions
        }
    }

    /**
     * 初始化前做的，子类方便实现自己的逻辑
     */
    open fun onCreateInit() {

    }


    open fun getSavedInstanceState(savedInstanceState: Bundle?) {
    }


    /**
     * 进行请求数据，只进行一次，如果需要点击重试，请写在requestData()
     */
    protected abstract fun initCommonData()


    protected abstract fun initCommonListener()

    /**
     * 请求数据，一般用于，请求失败点击重试时的网络请求
     */
    protected abstract fun requestCommonData()


    override fun showLoading() {
        statusHelper.showLoading()
    }

    override fun showContent() {
        statusHelper.showSuccess()
    }

    override fun showEmpty() {
        statusHelper.showEmpty()
    }

    override fun showError() {
        statusHelper.showFailed()
    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.removeActivity(mWeakReference)
    }

}
