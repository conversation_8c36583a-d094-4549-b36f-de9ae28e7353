package com.haoxue.libcommon.utils

import android.annotation.SuppressLint
import android.content.Context
import com.haoxue.libcommon.audio.OptimizedTtsPlayer
import com.lazy.library.logging.Logcat

/**
 * 优化的音频播放工具类
 * 使用MediaPlayer复用池提升TTS播放性能
 */
class OptimizedAudioPlaybackUtils {

    @SuppressLint("StaticFieldLeak")
    private var ttsPlayer: OptimizedTtsPlayer? = null

    /**
     * 初始化音频播放器
     * @param context 上下文
     */
    fun initialize(context: Context) {
        try {
            ttsPlayer = OptimizedTtsPlayer(context.applicationContext).apply {
                initialize()
            }
            Logcat.d("OptimizedAudioPlaybackUtils 初始化完成")
        } catch (e: Exception) {
            Logcat.e("初始化OptimizedAudioPlaybackUtils失败", e)
        }
    }

    /**
     * 添加音频到播放队列
     * @param audioUrl 音频文件 URL
     */
    fun playAudio(audioUrl: String) {
        try {
            val queueSizeBefore = ttsPlayer?.getQueueSize() ?: 0
            
            Logcat.d("TTS音频已添加到播放队列: $audioUrl")
            Logcat.d("添加前队列长度: $queueSizeBefore")
            
            // 添加到队列（可能会立即开始播放）
            ttsPlayer?.addToQueue(audioUrl)
            
        } catch (e: Exception) {
            Logcat.e("添加TTS音频到播放队列失败", e)
        }
    }

    /**
     * 批量添加音频到播放队列
     * @param audioUrls 音频文件 URL 列表
     */
    fun playAudioList(audioUrls: List<String>) {
        try {
            audioUrls.forEach { url ->
                playAudio(url)
            }
            Logcat.d("批量添加TTS音频到播放队列: ${audioUrls.size} 个")
        } catch (e: Exception) {
            Logcat.e("批量添加TTS音频失败", e)
        }
    }

    /**
     * 停止播放并清空队列
     */
    fun stop() {
        try {
            ttsPlayer?.stop()
            Logcat.d("TTS播放已停止")
        } catch (e: Exception) {
            Logcat.e("停止TTS播放失败", e)
        }
    }

    /**
     * 清空播放队列（但不停止当前播放）
     */
    fun clearQueue() {
        try {
            // OptimizedTtsPlayer暂时没有单独的clearQueue方法
            // 可以通过stop()来实现清空队列的效果
            stop()
            Logcat.d("TTS播放队列已清空")
        } catch (e: Exception) {
            Logcat.e("清空TTS播放队列失败", e)
        }
    }

    /**
     * 获取队列大小
     */
    fun getQueueSize(): Int {
        return try {
            ttsPlayer?.getQueueSize() ?: 0
        } catch (e: Exception) {
            Logcat.e("获取TTS队列大小失败", e)
            0
        }
    }

    /**
     * 获取当前播放状态
     */
    fun getCurrentState(): OptimizedTtsPlayer.TtsPlaybackState {
        return try {
            ttsPlayer?.getCurrentState() ?: OptimizedTtsPlayer.TtsPlaybackState.IDLE
        } catch (e: Exception) {
            Logcat.e("获取TTS播放状态失败", e)
            OptimizedTtsPlayer.TtsPlaybackState.IDLE
        }
    }

    /**
     * 设置播放状态监听器
     */
    fun setOnPlaybackStateChangedListener(listener: (OptimizedTtsPlayer.TtsPlaybackState) -> Unit) {
        try {
            ttsPlayer?.onPlaybackStateChanged = listener
        } catch (e: Exception) {
            Logcat.e("设置TTS播放状态监听器失败", e)
        }
    }

    /**
     * 设置播放错误监听器
     */
    fun setOnPlaybackErrorListener(listener: (String, Exception?) -> Unit) {
        try {
            ttsPlayer?.onPlaybackError = listener
        } catch (e: Exception) {
            Logcat.e("设置TTS播放错误监听器失败", e)
        }
    }

    /**
     * 设置队列变化监听器
     */
    fun setOnQueueChangedListener(listener: (Int) -> Unit) {
        try {
            ttsPlayer?.onQueueChanged = listener
        } catch (e: Exception) {
            Logcat.e("设置TTS队列变化监听器失败", e)
        }
    }

    /**
     * 设置播放结束后的回调
     */
    fun setOnPlayFinishListener(listener: (String) -> Unit) {
        try {
            ttsPlayer?.onPlayFinished = listener
        } catch (e: Exception) {
            Logcat.e("设置TTS播放完成监听器失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            ttsPlayer?.release()
            ttsPlayer = null
            Logcat.d("OptimizedAudioPlaybackUtils 资源已释放")
        } catch (e: Exception) {
            Logcat.e("释放OptimizedAudioPlaybackUtils资源失败", e)
        }
    }

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean {
        return ttsPlayer != null
    }

    /**
     * 获取播放器信息
     */
    fun getPlayerInfo(): String {
        return try {
            val state = getCurrentState()
            val queueSize = getQueueSize()
            "TTS播放器状态: $state, 队列长度: $queueSize"
        } catch (e: Exception) {
            "获取播放器信息失败: ${e.message}"
        }
    }
}
