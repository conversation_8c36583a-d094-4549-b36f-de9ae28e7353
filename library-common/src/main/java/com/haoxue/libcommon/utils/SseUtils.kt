package com.haoxue.libcommon.utils

import androidx.lifecycle.LifecycleCoroutineScope
import com.haoxue.libcommon.network.SimpleSseService
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.CancellationException

/**
 * SSE 工具类
 * 提供简化的 SSE 调用方法
 */
object SseUtils {

    private val sseService = SimpleSseService.getInstance()
    
    /**
     * 快速 AI 问答
     * @param lifecycleScope 生命周期协程作用域
     * @param question 问题
     * @param onMessage 消息回调（每个数据块）
     * @param onComplete 完成回调（完整响应）
     * @param onError 错误回调
     * @return Job 协程任务，可用于取消请求
     */
    fun quickAsk(
        lifecycleScope: LifecycleCoroutineScope,
        question: String,
        onMessage: (String) -> Unit = {},
        onComplete: (String) -> Unit = {},
        onError: (String) -> Unit = {}
    ): Job {
        return lifecycleScope.launch {
            try {
                sseService.askSimple(
                    question = question,
                    onMessage = onMessage,
                    onComplete = onComplete,
                    onError = { error ->
                        onError(error.message ?: "未知错误")
                        Logcat.e("快速 AI 问答失败", error)
                    }
                )
            } catch (e: CancellationException) {
                // 协程被取消，不调用错误回调，避免显示取消错误信息
                Logcat.d("SSE 请求被取消: ${e.message}")
                // 不重新抛出，避免触发错误回调
            } catch (e: Exception) {
                onError("调用异常: ${e.message}")
                Logcat.e("快速 AI 问答异常", e)
            }
        }
    }
    
    /**
     * 带认证的 AI 问答
     * @param lifecycleScope 生命周期协程作用域
     * @param question 问题
     * @param apiKey API 密钥
     * @param token 认证令牌
     * @param onMessage 消息回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     * @return Job 协程任务，可用于取消请求
     */
    fun askWithAuth(
        lifecycleScope: LifecycleCoroutineScope,
        question: String,
        apiKey: String? = null,
        token: String? = null,
        onMessage: (String) -> Unit,
        onComplete: (String) -> Unit = {},
        onError: (String) -> Unit = {}
    ): Job {
        return lifecycleScope.launch {
            try {
                val headers = mutableMapOf<String, String>()

                apiKey?.let { headers["X-API-Key"] = it }
                token?.let { headers["Authorization"] = "Bearer $it" }

                sseService.askSimple(
                    question = question,
                    onMessage = onMessage,
                    onComplete = onComplete,
                    onError = { error ->
                        onError(error.message ?: "认证失败")
                        Logcat.e("认证 AI 问答失败", error)
                    }
                )
            } catch (e: CancellationException) {
                // 协程被取消，不调用错误回调，避免显示取消错误信息
                Logcat.d("认证 SSE 请求被取消: ${e.message}")
                // 不重新抛出，避免触发错误回调
            } catch (e: Exception) {
                onError("认证调用异常: ${e.message}")
                Logcat.e("认证 AI 问答异常", e)
            }
        }
    }
    
    /**
     * 自定义 SSE 调用
     * @param lifecycleScope 生命周期协程作用域
     * @param question 问题
     * @param model AI 模型
     * @param temperature 温度参数
     * @param maxTokens 最大令牌数
     * @param headers 自定义请求头
     * @param onMessage 消息回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     * @return Job 协程任务，可用于取消请求
     */
    fun customAsk(
        lifecycleScope: LifecycleCoroutineScope,
        question: String,
        model: String = "gpt-3.5-turbo",
        temperature: String = "0.7",
        maxTokens: String = "2000",
        headers: Map<String, String> = emptyMap(),
        onMessage: (String) -> Unit,
        onComplete: (String) -> Unit = {},
        onError: (String) -> Unit = {}
    ): Job {
        return lifecycleScope.launch {
            try {
                val params = mapOf(
                    "model" to model,
                    "temperature" to temperature,
                    "max_tokens" to maxTokens
                )

                sseService.askSimple(
                    question = question,
                    onMessage = onMessage,
                    onComplete = onComplete,
                    onError = { error ->
                        onError(error.message ?: "自定义调用失败")
                        Logcat.e("自定义 AI 问答失败", error)
                    }
                )
            } catch (e: CancellationException) {
                // 协程被取消，不调用错误回调，避免显示取消错误信息
                Logcat.d("自定义 SSE 请求被取消: ${e.message}")
                // 不重新抛出，避免触发错误回调
            } catch (e: Exception) {
                onError("自定义调用异常: ${e.message}")
                Logcat.e("自定义 AI 问答异常", e)
            }
        }
    }
    
    /**
     * 批量问答（依次执行）
     * @param lifecycleScope 生命周期协程作用域
     * @param questions 问题列表
     * @param onQuestionStart 单个问题开始回调
     * @param onMessage 消息回调
     * @param onQuestionComplete 单个问题完成回调
     * @param onAllComplete 全部完成回调
     * @param onError 错误回调
     * @return Job 协程任务，可用于取消整个批量请求
     */
    fun batchAsk(
        lifecycleScope: LifecycleCoroutineScope,
        questions: List<String>,
        onQuestionStart: (Int, String) -> Unit = { _, _ -> },
        onMessage: (Int, String) -> Unit,
        onQuestionComplete: (Int, String) -> Unit = { _, _ -> },
        onAllComplete: (List<String>) -> Unit = {},
        onError: (Int, String) -> Unit = { _, _ -> }
    ): Job {
        return lifecycleScope.launch {
            val responses = mutableListOf<String>()
            
            questions.forEachIndexed { index, question ->
                try {
                    onQuestionStart(index, question)
                    
                    sseService.askSimple(
                        question = question,
                        onMessage = { content ->
                            onMessage(index, content)
                        },
                        onComplete = { fullResponse ->
                            responses.add(fullResponse)
                            onQuestionComplete(index, fullResponse)
                        },
                        onError = { error ->
                            responses.add("错误: ${error.message}")
                            onError(index, error.message ?: "未知错误")
                        }
                    )
                    
                } catch (e: Exception) {
                    responses.add("异常: ${e.message}")
                    onError(index, "调用异常: ${e.message}")
                }
            }
            
            onAllComplete(responses)
        }
    }
}
