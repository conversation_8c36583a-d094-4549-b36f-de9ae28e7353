package com.haoxue.libcommon.audio

import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.net.Uri
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentLinkedQueue


/**
 * 音频播放队列管理器
 * 按顺序播放 WebSocket 推送的音频文件
 */
class AudioQueuePlayer(private val context: Context, private var useAssets: Boolean = false) {

    private val audioQueue = ConcurrentLinkedQueue<String>()
    private var mediaPlayer: MediaPlayer? = null
    private var isPlayingFlag = false
    private var isPaused = false
    private var isPreparing = false  // 新增：正在准备播放的状态
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // 调试用：跟踪当前状态
    private var currentPlayingUrl: String? = null
    private var preparingUrl: String? = null

    // 播放状态回调
    private var onPlaybackStateChanged: ((AudioPlaybackState) -> Unit)? = null
    private var playFinishPath: ((path: String) -> Unit)? = null
    private var onPlaybackError: ((String, Exception?) -> Unit)? = null
    private var onQueueChanged: ((Int) -> Unit)? = null

    /**
     * 音频播放状态
     */
    enum class AudioPlaybackState {
        IDLE,       // 空闲
        LOADING,    // 加载中
        PLAYING,    // 播放中
        PAUSED,     // 暂停
        COMPLETED,  // 播放完成
        ERROR       // 错误
    }

    /**
     * 添加音频到播放队列
     * @param audioUrl 音频文件 URL
     */
    fun addToQueue(audioUrl: String) {
        try {
            if (audioUrl.isBlank()) {
                Logcat.w("音频 URL 为空，忽略")
                return
            }

            Logcat.d("🎵 准备添加音频到队列: $audioUrl")
            audioQueue.offer(audioUrl)
            val currentQueueSize = audioQueue.size
            Logcat.d("🎵 音频已添加，当前队列长度: $currentQueueSize")

            // 详细的状态日志
            logQueueState("添加音频: $audioUrl")

            // 通知队列变化（添加后的长度）
            onQueueChanged?.invoke(currentQueueSize)

            // 关键修复：只有在完全空闲时才开始播放
            if (!isPlayingFlag && !isPaused && !isPreparing) {
                Logcat.d("✅ 播放器空闲，立即开始播放，队列长度: $currentQueueSize")
                playNext()
            } else {
                Logcat.d("⏳ 播放器忙碌，音频已加入队列等待，当前状态: isPlaying=$isPlayingFlag, isPaused=$isPaused, isPreparing=$isPreparing")
            }

        } catch (e: Exception) {
            Logcat.e("添加音频到队列失败", e)
            onPlaybackError?.invoke("添加音频失败: ${e.message}", e)
        }
    }

    /**
     * 记录队列状态（调试用）
     */
    private fun logQueueState(action: String) {
        Logcat.d("=== 队列状态: $action ===")
        Logcat.d("队列长度: ${audioQueue.size}")
        Logcat.d("isPlayingFlag: $isPlayingFlag")
        Logcat.d("isPaused: $isPaused")
        Logcat.d("isPreparing: $isPreparing")
        Logcat.d("当前播放: $currentPlayingUrl")
        Logcat.d("正在准备: $preparingUrl")
        Logcat.d("队列内容: ${audioQueue.take(3)}${if (audioQueue.size > 3) "..." else ""}")
    }

    /**
     * 播放下一个音频
     */
    private fun playNext() {
        scope.launch {
            try {
                val currentQueueSize = audioQueue.size
                Logcat.d("🎵 准备播放下一个音频，当前队列长度: $currentQueueSize")

                val nextUrl = audioQueue.poll()
                if (nextUrl != null) {
                    val remainingQueueSize = audioQueue.size
                    Logcat.d("🎵 从队列取出音频: $nextUrl")
                    Logcat.d("🎵 取出后剩余队列长度: $remainingQueueSize")

                    // 通知队列变化（取出后的长度）
                    onQueueChanged?.invoke(remainingQueueSize)

                    playAudio(nextUrl)
                } else {
                    // 队列为空，设置为空闲状态
                    isPlayingFlag = false
                    onPlaybackStateChanged?.invoke(AudioPlaybackState.IDLE)
                    onQueueChanged?.invoke(0)
                    Logcat.d("🎵 音频队列为空，播放结束")
                }
            } catch (e: Exception) {
                Logcat.e("播放下一个音频失败", e)
                onPlaybackError?.invoke("播放失败: ${e.message}", e)
                // 继续尝试播放下一个
                playNext()
            }
        }
    }

    /**
     * 播放指定音频
     */
    private suspend fun playAudio(audioUrl: String) {
        try {
            // 设置准备状态
            isPreparing = true
            preparingUrl = audioUrl

            Logcat.d("=== 开始准备音频 ===")
            Logcat.d("准备播放: $audioUrl")
            Logcat.d("当前队列剩余: ${audioQueue.size}")

            onPlaybackStateChanged?.invoke(AudioPlaybackState.LOADING)

            // 释放之前的 MediaPlayer
            releaseMediaPlayer()

            // 创建新的 MediaPlayer
            mediaPlayer = MediaPlayer().apply {
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .build()
                )

                // 设置数据源
                if (audioUrl.startsWith("http://") || audioUrl.startsWith("https://")) {
                    setDataSource(audioUrl)
                } else if (useAssets) {
                    val afd: AssetFileDescriptor = context.assets.openFd(audioUrl)
                    setDataSource(afd.fileDescriptor, afd.startOffset, afd.getLength())
                } else {
                    setDataSource(context, Uri.parse(audioUrl))
                }

                // 设置监听器
                setOnPreparedListener { player ->
                    try {
                        // 关键修复：准备完成后更新所有状态
                        isPreparing = false
                        isPlayingFlag = true
                        isPaused = false
                        currentPlayingUrl = audioUrl
                        preparingUrl = null

                        player.start()
                        onPlaybackStateChanged?.invoke(AudioPlaybackState.PLAYING)

//                        Logcat.d("=== 音频准备完成，开始播放 ===")
                        Logcat.d("开始播放: $audioUrl")
//                        Logcat.d("队列剩余: ${audioQueue.size}")

                    } catch (e: Exception) {
                        Logcat.e("启动播放失败", e)
                        onPlaybackError?.invoke("启动播放失败: ${e.message}", e)

                        // 播放失败，重置状态并继续播放下一个
                        isPreparing = false
                        isPlayingFlag = false
                        currentPlayingUrl = null
                        preparingUrl = null
                        playNext()
                    }
                }

                setOnCompletionListener { _ ->
                    try {
//                        Logcat.d("=== 音频播放完成 ===")
                        Logcat.d("完成播放: $audioUrl")
//                        Logcat.d("队列剩余: ${audioQueue.size}")

                        onPlaybackStateChanged?.invoke(AudioPlaybackState.COMPLETED)
                        playFinishPath?.invoke(audioUrl)

                        // 重置播放状态
                        isPlayingFlag = false
                        isPaused = false
                        currentPlayingUrl = null

                        // 播放下一个音频
                        if (audioQueue.size > 0) {
                            Logcat.d("✅ 继续播放队列中的下一个音频")
                            playNext()
                        } else {
                            Logcat.d("🏁 队列已空，播放结束")
                            onPlaybackStateChanged?.invoke(AudioPlaybackState.IDLE)
                        }
                    } catch (e: Exception) {
                        Logcat.e("播放完成处理异常", e)
                        isPlayingFlag = false
                        currentPlayingUrl = null
                        playNext()
                    }
                }

                setOnErrorListener { _, what, extra ->
                    try {
                        val errorMsg = "MediaPlayer 错误: what=$what, extra=$extra, 音频: $audioUrl"
                        Logcat.e("=== 音频播放错误 ===")
                        Logcat.e(errorMsg)

                        onPlaybackStateChanged?.invoke(AudioPlaybackState.ERROR)
                        onPlaybackError?.invoke(errorMsg, null)

                        // 重置所有状态
                        isPreparing = false
                        isPlayingFlag = false
                        isPaused = false
                        currentPlayingUrl = null
                        preparingUrl = null

                        // 尝试播放下一个音频
                        Logcat.d("❌ 播放出错，尝试播放下一个音频，队列剩余: ${audioQueue.size}")
                        playNext()
                    } catch (e: Exception) {
                        Logcat.e("错误处理异常", e)
                    }
                    true // 返回 true 表示错误已处理
                }

                // 异步准备
                prepareAsync()
            }

        } catch (e: Exception) {
            Logcat.e("播放音频异常: $audioUrl", e)
            onPlaybackStateChanged?.invoke(AudioPlaybackState.ERROR)
            onPlaybackError?.invoke("播放音频失败: ${e.message}", e)

            // 重置所有状态
            isPreparing = false
            isPlayingFlag = false
            isPaused = false
            currentPlayingUrl = null
            preparingUrl = null

            // 尝试播放下一个音频
            playNext()
        }
    }

    /**
     * 暂停播放
     */
    fun pause() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.pause()
                    isPaused = true
                    isPlayingFlag = false
                    onPlaybackStateChanged?.invoke(AudioPlaybackState.PAUSED)
                    Logcat.d("音频播放已暂停")
                }
            }
        } catch (e: Exception) {
            Logcat.e("暂停播放失败", e)
            onPlaybackError?.invoke("暂停失败: ${e.message}", e)
        }
    }

    /**
     * 恢复播放
     */
    fun resume() {
        try {
            mediaPlayer?.let { player ->
                if (isPaused) {
                    player.start()
                    isPaused = false
                    isPlayingFlag = true
                    onPlaybackStateChanged?.invoke(AudioPlaybackState.PLAYING)
                    Logcat.d("音频播放已恢复")
                }
            }
        } catch (e: Exception) {
            Logcat.e("恢复播放失败", e)
            onPlaybackError?.invoke("恢复播放失败: ${e.message}", e)
        }
    }

    /**
     * 停止播放并清空队列
     */
    fun stop() {
        try {
            releaseMediaPlayer()
            audioQueue.clear()

            // 重置所有状态
            isPreparing = false
            isPlayingFlag = false
            isPaused = false
            currentPlayingUrl = null
            preparingUrl = null

            onPlaybackStateChanged?.invoke(AudioPlaybackState.IDLE)
            onQueueChanged?.invoke(0)
            Logcat.d("音频播放已停止，队列已清空")
        } catch (e: Exception) {
            Logcat.e("停止播放失败", e)
        }
    }

    /**
     * 停止播放并清空队列
     */
    fun clearQueue() {
        try {
            audioQueue.clear()
            Logcat.d("队列已清空")
        } catch (e: Exception) {
        }
    }

    /**
     * 跳过当前音频，播放下一个
     */
    fun skipCurrent() {
        try {
            releaseMediaPlayer()

            // 重置状态
            isPreparing = false
            isPlayingFlag = false
            isPaused = false
            currentPlayingUrl = null
            preparingUrl = null

            Logcat.d("跳过当前音频")
            playNext()
        } catch (e: Exception) {
            Logcat.e("跳过当前音频失败", e)
            onPlaybackError?.invoke("跳过失败: ${e.message}", e)
        }
    }

    /**
     * 获取队列长度
     */
    fun getQueueSize(): Int = audioQueue.size

    /**
     * 获取当前播放状态
     */
    fun getCurrentState(): AudioPlaybackState {
        return when {
            isPreparing -> AudioPlaybackState.LOADING
            isPlayingFlag -> AudioPlaybackState.PLAYING
            isPaused -> AudioPlaybackState.PAUSED
            else -> AudioPlaybackState.IDLE
        }
    }

    /**
     * 设置播放状态监听器
     */
    fun setOnPlaybackStateChangedListener(listener: (AudioPlaybackState) -> Unit) {
        onPlaybackStateChanged = listener
    }

    /**
     * 设置播放状态监听器
     */
    fun setOnPlayFinishListener(listener: (path: String) -> Unit) {
        playFinishPath = listener
    }

    /**
     * 设置播放错误监听器
     */
    fun setOnPlaybackErrorListener(listener: (String, Exception?) -> Unit) {
        onPlaybackError = listener
    }

    /**
     * 设置队列变化监听器
     */
    fun setOnQueueChangedListener(listener: (Int) -> Unit) {
        onQueueChanged = listener
    }

    /**
     * 释放 MediaPlayer 资源
     */
    private fun releaseMediaPlayer() {
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                reset()
                release()
            }
            mediaPlayer = null
        } catch (e: Exception) {
            Logcat.e("释放 MediaPlayer 失败", e)
        }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        try {
            stop()
            scope.cancel()
            onPlaybackStateChanged = null
            onPlaybackError = null
            onQueueChanged = null
            Logcat.d("AudioQueuePlayer 资源已释放")
        } catch (e: Exception) {
            Logcat.e("释放资源失败", e)
        }
    }
}
