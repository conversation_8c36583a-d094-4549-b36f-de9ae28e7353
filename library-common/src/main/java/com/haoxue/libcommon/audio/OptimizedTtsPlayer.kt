package com.haoxue.libcommon.audio

import android.content.Context
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.net.Uri
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 优化的TTS音频播放器
 * 
 * 优化策略：
 * 1. MediaPlayer复用池，避免频繁创建/销毁
 * 2. 预创建多个MediaPlayer实例
 * 3. 智能队列管理
 * 4. 异步准备和播放
 */
class OptimizedTtsPlayer(private val context: Context) {
    
    companion object {
        private const val PLAYER_POOL_SIZE = 3 // MediaPlayer池大小
    }
    
    // MediaPlayer复用池
    private val availablePlayers = ConcurrentLinkedQueue<MediaPlayer>()
    private val busyPlayers = mutableSetOf<MediaPlayer>()
    
    // 播放队列
    private val audioQueue = ConcurrentLinkedQueue<String>()
    
    // 状态管理
    private var isInitialized = false
    private var isPlaying = false
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 回调接口
    var onPlaybackStateChanged: ((TtsPlaybackState) -> Unit)? = null
    var onQueueChanged: ((Int) -> Unit)? = null
    var onPlaybackError: ((String, Exception?) -> Unit)? = null
    var onPlayFinished: ((String) -> Unit)? = null
    
    enum class TtsPlaybackState {
        IDLE, PLAYING, PAUSED, ERROR
    }
    
    /**
     * 初始化播放器池
     */
    fun initialize() {
        if (isInitialized) return
        
        scope.launch(Dispatchers.IO) {
            try {
                // 预创建MediaPlayer池
                repeat(PLAYER_POOL_SIZE) {
                    val player = createMediaPlayer()
                    availablePlayers.offer(player)
                }
                
                isInitialized = true
                Logcat.d("OptimizedTtsPlayer 初始化完成，创建了 $PLAYER_POOL_SIZE 个MediaPlayer")
                
            } catch (e: Exception) {
                Logcat.e("OptimizedTtsPlayer 初始化失败", e)
                onPlaybackError?.invoke("初始化失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 创建配置好的MediaPlayer
     */
    private fun createMediaPlayer(): MediaPlayer {
        return MediaPlayer().apply {
            setAudioAttributes(
                AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .build()
            )
        }
    }
    
    /**
     * 添加音频到播放队列
     */
    fun addToQueue(audioUrl: String) {
        if (!isInitialized) {
            Logcat.w("播放器未初始化，先初始化")
            initialize()
        }
        
        if (audioUrl.isBlank()) {
            Logcat.w("音频URL为空，忽略")
            return
        }
        
        audioQueue.offer(audioUrl)
        val queueSize = audioQueue.size
        onQueueChanged?.invoke(queueSize)
        
        Logcat.d("TTS音频已添加到队列: $audioUrl, 队列长度: $queueSize")
        
        // 如果当前没有播放，开始播放
        if (!isPlaying) {
            playNext()
        }
    }
    
    /**
     * 播放下一个音频
     */
    private fun playNext() {
        scope.launch {
            try {
                val nextUrl = audioQueue.poll()
                if (nextUrl != null) {
                    onQueueChanged?.invoke(audioQueue.size)
                    playAudio(nextUrl)
                } else {
                    // 队列为空
                    isPlaying = false
                    onPlaybackStateChanged?.invoke(TtsPlaybackState.IDLE)
                    onQueueChanged?.invoke(0)
                    Logcat.d("TTS播放队列为空，播放结束")
                }
            } catch (e: Exception) {
                Logcat.e("播放下一个TTS音频失败", e)
                onPlaybackError?.invoke("播放失败: ${e.message}", e)
                // 继续尝试播放下一个
                playNext()
            }
        }
    }
    
    /**
     * 播放指定音频
     */
    private suspend fun playAudio(audioUrl: String) = withContext(Dispatchers.IO) {
        try {
            val player = getAvailablePlayer()
            if (player == null) {
                Logcat.w("没有可用的MediaPlayer，创建新的")
                val newPlayer = createMediaPlayer()
                playWithPlayer(newPlayer, audioUrl)
            } else {
                playWithPlayer(player, audioUrl)
            }
        } catch (e: Exception) {
            Logcat.e("播放TTS音频失败: $audioUrl", e)
            onPlaybackError?.invoke("播放失败: ${e.message}", e)
            playNext()
        }
    }
    
    /**
     * 获取可用的MediaPlayer
     */
    private fun getAvailablePlayer(): MediaPlayer? {
        return availablePlayers.poll()
    }
    
    /**
     * 使用指定的MediaPlayer播放音频
     */
    private suspend fun playWithPlayer(player: MediaPlayer, audioUrl: String) = withContext(Dispatchers.IO) {
        try {
            // 重置播放器状态
            player.reset()
            
            // 设置数据源
            if (audioUrl.startsWith("http://") || audioUrl.startsWith("https://")) {
                player.setDataSource(audioUrl)
            } else {
                player.setDataSource(context, Uri.parse(audioUrl))
            }
            
            // 标记为忙碌
            busyPlayers.add(player)
            isPlaying = true
            
            // 设置监听器
            player.setOnPreparedListener { preparedPlayer ->
                try {
                    preparedPlayer.start()
                    withContext(Dispatchers.Main) {
                        onPlaybackStateChanged?.invoke(TtsPlaybackState.PLAYING)
                    }
                    Logcat.d("开始播放TTS: $audioUrl")
                } catch (e: Exception) {
                    Logcat.e("启动TTS播放失败", e)
                    releasePlayer(preparedPlayer)
                    playNext()
                }
            }
            
            player.setOnCompletionListener { completedPlayer ->
                try {
                    Logcat.d("TTS播放完成: $audioUrl")
                    onPlayFinished?.invoke(audioUrl)
                    releasePlayer(completedPlayer)
                    playNext()
                } catch (e: Exception) {
                    Logcat.e("TTS播放完成处理失败", e)
                    releasePlayer(completedPlayer)
                    playNext()
                }
            }
            
            player.setOnErrorListener { errorPlayer, what, extra ->
                Logcat.e("TTS播放错误: what=$what, extra=$extra")
                onPlaybackError?.invoke("播放错误: what=$what, extra=$extra", null)
                releasePlayer(errorPlayer)
                playNext()
                true
            }
            
            // 异步准备
            player.prepareAsync()
            
        } catch (e: Exception) {
            Logcat.e("准备TTS播放失败: $audioUrl", e)
            releasePlayer(player)
            playNext()
        }
    }
    
    /**
     * 释放MediaPlayer回到池中
     */
    private fun releasePlayer(player: MediaPlayer) {
        try {
            busyPlayers.remove(player)
            
            // 如果池未满，回收到池中
            if (availablePlayers.size < PLAYER_POOL_SIZE) {
                availablePlayers.offer(player)
            } else {
                // 池已满，直接释放
                player.release()
            }
        } catch (e: Exception) {
            Logcat.e("释放MediaPlayer失败", e)
            try {
                player.release()
            } catch (releaseException: Exception) {
                Logcat.e("强制释放MediaPlayer失败", releaseException)
            }
        }
    }
    
    /**
     * 停止播放并清空队列
     */
    fun stop() {
        try {
            // 停止所有正在播放的MediaPlayer
            busyPlayers.forEach { player ->
                try {
                    if (player.isPlaying) {
                        player.stop()
                    }
                } catch (e: Exception) {
                    Logcat.e("停止MediaPlayer失败", e)
                }
            }
            
            // 清空队列
            audioQueue.clear()
            isPlaying = false
            
            onPlaybackStateChanged?.invoke(TtsPlaybackState.IDLE)
            onQueueChanged?.invoke(0)
            
            Logcat.d("TTS播放已停止，队列已清空")
            
        } catch (e: Exception) {
            Logcat.e("停止TTS播放失败", e)
        }
    }
    
    /**
     * 获取队列大小
     */
    fun getQueueSize(): Int = audioQueue.size
    
    /**
     * 获取当前状态
     */
    fun getCurrentState(): TtsPlaybackState {
        return when {
            isPlaying -> TtsPlaybackState.PLAYING
            else -> TtsPlaybackState.IDLE
        }
    }
    
    /**
     * 释放所有资源
     */
    fun release() {
        try {
            scope.cancel()
            
            // 释放所有MediaPlayer
            (availablePlayers + busyPlayers).forEach { player ->
                try {
                    player.release()
                } catch (e: Exception) {
                    Logcat.e("释放MediaPlayer失败", e)
                }
            }
            
            availablePlayers.clear()
            busyPlayers.clear()
            audioQueue.clear()
            
            isInitialized = false
            isPlaying = false
            
            Logcat.d("OptimizedTtsPlayer 资源已释放")
            
        } catch (e: Exception) {
            Logcat.e("释放OptimizedTtsPlayer资源失败", e)
        }
    }
}
