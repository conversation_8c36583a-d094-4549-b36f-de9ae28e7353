<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="wearDetails" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:background">@color/transparent</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <declare-styleable name="SpinnerPopup">
        <attr name="spinner_text" format="string" />
        <attr name="text_color" format="color" />
        <attr name="text_size" format="dimension" />
        <attr name="icon" format="reference" />
        <attr name="background_color" format="color" />
    </declare-styleable>

    <declare-styleable name="VerticalCenterImageView">
        <attr name="image" format="reference" />
        <attr name="text" format="string" />
    </declare-styleable>

    <declare-styleable name="ShadowLayout">
        <attr name="shadowRadius" format="dimension" />
        <attr name="shadowLength" format="dimension" />
        <attr name="shadowColor" format="color" />
        <attr name="shadowTranslationX" format="dimension" />
        <attr name="shadowTranslationY" format="dimension" />
        <attr name="shadowDirection">
            <flag name="all" value="0x1111" />
            <flag name="left" value="0x0001" />
            <flag name="top" value="0x0010" />
            <flag name="right" value="0x0100" />
            <flag name="bottom" value="0x1000" />
        </attr>
    </declare-styleable>

    <declare-styleable name="CommonNumberPicker">
        <attr name="addBold" format="boolean" />
    </declare-styleable>


    <!-- common dialog -->
    <style name="common_Dialog" parent="@style/Theme.AppCompat.Light.Dialog">

        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <!-- 是否启用标题栏 -->
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="common_dialogAnimation">
        <item name="android:windowEnterAnimation">@anim/common_dialog_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/common_dialog_exit_anim</item>
    </style>


    <style name="tabLayoutCustomer">
        <item name="android:textSize">18dp</item>
    </style>

    <style name="noWindowBottomSheetDialog" parent="Theme.Design.BottomSheetDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item><!--没有标题-->
        <item name="android:windowBackground">@android:color/transparent</item><!--窗口背景色透明-->
        <item name="android:windowIsFloating">true</item><!--是否浮在界面上-->
        <!--<item name="android:backgroundDimEnabled">false</item>&lt;!&ndash;背景是否模糊显示&ndash;&gt;-->
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <!--<item name="android:background">@android:color/transparent</item>-->
        <item name="android:colorBackground">@android:color/transparent</item>

    </style>


</resources>