<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="20dp"
    android:paddingLeft="4dp"
    android:paddingRight="4dp">

    <!-- 左侧消息 (AI) -->
    <TextView
        android:id="@+id/tv_content_left"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="60dp"
        android:background="@drawable/bg_chat_left"
        android:padding="12dp"
        android:textColor="@android:color/black"
        android:textSize="14sp"
        android:lineSpacingExtra="4dp"
        android:textIsSelectable="true"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="这是 AI 的回复消息，支持 **Markdown** 渲染"
        tools:visibility="visible" />

    <!-- 右侧消息 (用户) -->
    <TextView
        android:id="@+id/tv_content_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="60dp"
        android:background="@drawable/bg_chat_right"
        android:padding="12dp"
        android:textColor="@color/common_color_black"
        android:textSize="14sp"
        android:lineSpacingExtra="4dp"
        android:textIsSelectable="true"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="这是用户的消息"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
