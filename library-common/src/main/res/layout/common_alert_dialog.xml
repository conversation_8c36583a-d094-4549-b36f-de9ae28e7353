<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical">


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/common_color_white"
        app:cardCornerRadius="10dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_color_FFFFFF"
            android:gravity="center_horizontal"
            android:minWidth="280dp"
            android:orientation="vertical"
            android:paddingTop="18dp">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:textColor="@color/common_color_333333"
                android:textSize="21dp"
                tools:text="提示" />

            <TextView
                android:id="@+id/tvMsg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="18dp"
                android:lineSpacingExtra="6dp"
                android:textColor="@color/common_color_333333"
                android:textSize="19dp"
                tools:text="告知当前状态，信息和解决方法,告知当前状态，信息和解决方法" />

            <include layout="@layout/common_divider_light_horizontal_f5f7fa" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    android:visibility="gone"
                    android:gravity="center"
                    android:id="@+id/btnNature"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/common_color_FFFFFF"
                    android:textColor="@color/theme_color_FF4A36"
                    android:textSize="20dp"
                    tools:text="确定" />

                <LinearLayout
                    android:id="@+id/btnsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:gravity="center"
                        android:id="@+id/btnNeg"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@color/common_color_FFFFFF"
                        android:textColor="@color/common_color_999999"
                        android:textSize="20dp"
                        tools:text="取消" />

                    <include layout="@layout/common_divider_light_vertical" />

                    <TextView
                        android:gravity="center"
                        android:id="@+id/btnPos"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@color/common_color_FFFFFF"
                        android:textColor="@color/theme_color"
                        android:textSize="20dp"
                        tools:text="确定" />
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>