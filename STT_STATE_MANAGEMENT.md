# 📱 STT状态管理系统

## 🎯 **功能概述**

实现了一个全局的STT状态管理系统，根据当前页面状态决定STT的工作模式：
- **McpActivity**: 完整模式（本地工具拦截 + SSE接口调用）
- **其他Activity**: 仅本地模式（只执行本地工具拦截，不调用SSE接口）

## 🔧 **核心组件**

### **1. SttStateManager (状态管理器)**

```kotlin
object SttStateManager {
    enum class SttMode {
        FULL_MODE,      // 完整模式：本地工具拦截 + SSE接口调用
        LOCAL_ONLY      // 仅本地模式：只执行本地工具拦截
    }
    
    fun setFullMode(activityName: String)     // 设置完整模式
    fun setLocalOnlyMode(activityName: String) // 设置仅本地模式
    fun shouldCallSse(): Boolean              // 检查是否应该调用SSE
    fun shouldExecuteLocalTools(): Boolean    // 检查是否执行本地工具
}
```

### **2. McpActivity (主要页面)**

```kotlin
override fun onResume() {
    super.onResume()
    SttStateManager.setFullMode("McpActivity")
}

override fun onPause() {
    super.onPause()
    SttStateManager.setLocalOnlyMode("Background")
}
```

### **3. 其他Activity (示例)**

```kotlin
override fun onResume() {
    super.onResume()
    SttStateManager.setLocalOnlyMode("MainActivity")
}
```

## 🚀 **工作流程**

### **在McpActivity中**
```
1. 用户说话 → STT识别文本
2. 检查本地工具拦截 → 如果匹配，直接返回结果
3. 如果不匹配 → 调用SSE接口获取AI回复
4. 显示AI回复
```

### **在其他Activity中**
```
1. 用户说话 → STT识别文本
2. 检查本地工具拦截 → 如果匹配，直接返回结果
3. 如果不匹配 → 不调用SSE，只记录日志
4. 不显示AI回复
```

## 📝 **使用方法**

### **为新Activity添加STT状态管理**

1. **在Activity的onResume中设置状态**：
```kotlin
override fun onResume() {
    super.onResume()
    SttStateManager.setLocalOnlyMode("YourActivityName")
}
```

2. **（可选）在onPause中重置状态**：
```kotlin
override fun onPause() {
    super.onPause()
    // 如果需要，可以设置为其他状态
}
```

### **检查当前状态**

```kotlin
// 检查是否应该调用SSE
if (SttStateManager.shouldCallSse()) {
    // 调用SSE接口
}

// 检查是否执行本地工具
if (SttStateManager.shouldExecuteLocalTools()) {
    // 执行本地工具拦截
}

// 获取状态描述
val status = SttStateManager.getStatusDescription()
Logcat.d("当前状态: $status")
```

## 🔍 **关键日志监控**

### **状态切换日志**
```
📱 STT状态: 切换到完整模式 (McpActivity)
📱 STT状态: 切换到仅本地模式 (MainActivity)
```

### **处理逻辑日志**
```
📱 STT模式: FULL_MODE, 活跃页面: McpActivity，调用SSE接口: 你好
📱 STT模式: LOCAL_ONLY, 活跃页面: MainActivity，跳过SSE调用: 你好
📱 本地工具拦截处理: 跳绳开始
```

## ⚙️ **配置选项**

### **自定义工作模式**

可以根据需要扩展SttMode枚举：

```kotlin
enum class SttMode {
    FULL_MODE,          // 完整模式
    LOCAL_ONLY,         // 仅本地模式
    SILENT_MODE,        // 静默模式（完全禁用STT）
    CUSTOM_MODE         // 自定义模式
}
```

### **动态切换模式**

```kotlin
// 临时切换到仅本地模式
SttStateManager.setLocalOnlyMode("TemporaryMode")

// 恢复完整模式
SttStateManager.setFullMode("McpActivity")
```

## 🎯 **预期效果**

1. **在McpActivity中**：
   - STT识别 → 本地工具拦截 → SSE接口调用 → AI回复
   - 完整的对话体验

2. **在其他Activity中**：
   - STT识别 → 本地工具拦截 → 不调用SSE
   - 只执行本地命令，不产生网络请求

3. **状态同步**：
   - 页面切换时自动更新STT工作模式
   - 全局状态管理，避免状态混乱

## ✅ **测试验证**

1. **在McpActivity中说话** → 应该看到AI回复
2. **跳转到其他Activity后说话** → 只执行本地工具，无AI回复
3. **返回McpActivity后说话** → 恢复AI回复功能
4. **检查日志** → 确认状态切换和处理逻辑正确

这个系统确保了STT在不同页面有不同的行为，避免了在非主要页面产生不必要的网络请求和AI回复。
