# 🎵 音频打断按钮测试指南

## 🎯 **测试目标**

验证新增的专用音频打断按钮功能是否正常工作，确保STT录制不会录制到扬声器声音。

## 🔧 **测试环境准备**

### **1. 编译运行**
```bash
# 确保项目编译成功
./gradlew assembleDebug

# 安装到设备
adb install app/build/outputs/apk/debug/app-debug.apk
```

### **2. 检查布局**
- 启动应用，进入McpActivity
- 确认界面右下角有两个按钮：
  - 下方：蓝色滚动按钮（scrollToBottomFab）
  - 上方：红色打断按钮（interruptAudioFab，初始隐藏）

## 🧪 **功能测试步骤**

### **测试1: 基础显示/隐藏**

1. **初始状态检查**
   - ✅ 打断按钮应该隐藏（visibility=gone）
   - ✅ 滚动按钮根据内容显示/隐藏

2. **触发音频播放**
   - 点击"开始"按钮，说话触发AI回复
   - 当AI开始回复音频时：
   - ✅ 打断按钮应该自动显示
   - ✅ 按钮显示红色暂停图标

3. **播放完成**
   - 等待音频播放完成
   - ✅ 打断按钮应该自动隐藏

### **测试2: STT阻止功能**

1. **播放期间STT测试**
   - 触发AI音频回复
   - 在播放期间点击"开始"录音
   - 对着麦克风说话
   - ✅ 检查日志：应该看到"STT 被阻止: 音频播放中，忽略文本"

2. **日志验证**
   ```
   预期日志：
   🎵 音频播放开始，禁用STT处理
   STT 被阻止: 音频播放中，忽略文本 'xxx'
   🎵 显示音频打断按钮
   ```

### **测试3: 打断功能**

1. **主动打断测试**
   - 触发AI音频回复
   - 在播放期间点击红色打断按钮
   - ✅ 音频应该立即停止
   - ✅ 打断按钮应该立即隐藏
   - ✅ STT功能应该立即恢复

2. **打断后录音测试**
   - 打断音频后立即点击"开始"录音
   - 说话测试
   - ✅ STT应该正常工作，不再被阻止

3. **日志验证**
   ```
   预期日志：
   🎵 用户主动打断音频播放
   🎵 音频已打断，STT处理已启用
   🎵 隐藏音频打断按钮
   ```

### **测试4: 按钮独立性**

1. **滚动按钮测试**
   - 在没有音频播放时
   - ✅ 滚动按钮应该独立工作
   - ✅ 点击应该滚动到底部

2. **长按功能测试**
   - 长按滚动按钮
   - ✅ 应该触发WebSocket重连

## 🔍 **关键日志监控**

### **正常流程日志**
```
🎵 音频播放开始，禁用STT处理
🎵 显示音频打断按钮
STT 被阻止: 音频播放中，忽略文本 'xxx'
🎵 音频播放完成，启用STT处理
🎵 隐藏音频打断按钮
```

### **打断流程日志**
```
🎵 用户主动打断音频播放
🎵 音频已打断，STT处理已启用
🎵 隐藏音频打断按钮
```

## ⚠️ **常见问题排查**

### **问题1: 打断按钮不显示**
- 检查音频播放状态监听器是否正确设置
- 确认`onAudioPlaybackStarted()`是否被调用
- 检查`updateInterruptButtonState()`方法

### **问题2: STT仍然被录制**
- 确认`canProcessStt`状态是否正确更新
- 检查`handleSttTextWithDebounce()`中的阻止逻辑
- 验证音频播放状态变化事件

### **问题3: 按钮位置重叠**
- 检查布局约束是否正确
- 确认`app:layout_constraintBottom_toTopOf="@id/scrollToBottomFab"`

## ✅ **测试通过标准**

1. **显示控制**: 播放时显示，空闲时隐藏
2. **STT阻止**: 播放期间STT输入被正确忽略
3. **打断功能**: 点击按钮立即停止播放并恢复STT
4. **状态同步**: 音频状态与按钮状态保持同步
5. **独立性**: 不影响滚动按钮的正常功能

## 🎯 **预期效果**

- ✅ **解决回音问题**: 播放时STT被禁用，避免录制扬声器声音
- ✅ **用户控制**: 提供明确的打断机制
- ✅ **状态清晰**: 按钮状态明确反映当前播放状态
- ✅ **体验流畅**: 打断操作即时响应，STT立即恢复

通过这些测试，确保音频打断功能完全符合预期，有效解决STT录制扬声器声音的问题！
