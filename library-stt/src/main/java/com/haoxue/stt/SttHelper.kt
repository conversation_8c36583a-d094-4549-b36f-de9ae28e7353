package com.haoxue.stt

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.media.audiofx.AcousticEchoCanceler
import android.media.audiofx.AutomaticGainControl
import android.media.audiofx.NoiseSuppressor
import com.haoxue.libcommon.utils.PermissionHelper
import com.k2fsa.sherpa.ncnn.RecognizerConfig
import com.k2fsa.sherpa.ncnn.SherpaNcnn
import com.k2fsa.sherpa.ncnn.getDecoderConfig
import com.k2fsa.sherpa.ncnn.getFeatureExtractorConfig
import com.k2fsa.sherpa.ncnn.getModelConfig
import com.lazy.library.logging.Logcat
import kotlin.concurrent.thread

object SttHelper {
    private const val useGPU = true
    private lateinit var model: SherpaNcnn
    private val sampleRateInHz = 16000
    private val channelConfig = AudioFormat.CHANNEL_IN_MONO
    private val audioFormat = AudioFormat.ENCODING_PCM_16BIT
    private var audioRecord: AudioRecord? = null
    private var recordingThread: Thread? = null
    private val audioSource = MediaRecorder.AudioSource.VOICE_COMMUNICATION

    // 音频增强组件
    private var noiseSuppressor: NoiseSuppressor? = null
    private var acousticEchoCanceler: AcousticEchoCanceler? = null
    private var automaticGainControl: AutomaticGainControl? = null
    @Volatile
    private var isRecording: Boolean = false
    @Volatile
    private var isPaused: Boolean = false

    fun init(context: Context) {

        if (::model.isInitialized) return

        val featConfig = getFeatureExtractorConfig(
            sampleRate = 16000.0f,
            featureDim = 80
        )
        val modelConfig = getModelConfig(useGPU)!!
        val decoderConfig = getDecoderConfig(method = "greedy_search", numActivePaths = 4)

        val config = RecognizerConfig(
            featConfig = featConfig,
            modelConfig = modelConfig,
            decoderConfig = decoderConfig,
            enableEndpoint = true,
            rule1MinTrailingSilence = 1.5f,
            rule2MinTrailingSilence = 0.6f,
            rule3MinUtteranceLength = 15.0f,
            // 热词配置
            hotwordsFile = "hotwords.txt",  // assets目录下的热词文件
            hotwordsScore = 1.8f,           // 热词权重分数
        )
        model = SherpaNcnn(
            assetManager = context.assets,
            config = config,
        )
    }

    /**
     * 初始化音频增强功能
     */
    private fun initAudioEnhancements() {
        try {
            val audioSessionId = audioRecord?.audioSessionId ?: return

            // 噪音抑制
            if (NoiseSuppressor.isAvailable()) {
                noiseSuppressor = NoiseSuppressor.create(audioSessionId)?.apply {
                    enabled = true
                }
                Logcat.d("噪音抑制已启用")
            }

            // 回声消除
            if (AcousticEchoCanceler.isAvailable()) {
                acousticEchoCanceler = AcousticEchoCanceler.create(audioSessionId)?.apply {
                    enabled = true
                }
                Logcat.d("回声消除已启用")
            }

            // 自动增益控制
            if (AutomaticGainControl.isAvailable()) {
                automaticGainControl = AutomaticGainControl.create(audioSessionId)?.apply {
                    enabled = true
                }
                Logcat.d("自动增益控制已启用")
            }

        } catch (e: Exception) {
            Logcat.e("初始化音频增强失败", e)
        }
    }



    fun startRecord(context: Context, statusBack: (Boolean) -> Unit, callback: (String) -> Unit) {
        // 切换到通话模式，让系统尽可能使用带有回声消除链路的音频路径
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
        val previousAudioMode = audioManager.mode
        audioManager.mode = android.media.AudioManager.MODE_IN_COMMUNICATION

        PermissionHelper.requestPermission(context, arrayListOf(Manifest.permission.RECORD_AUDIO)) {
            if (it) {
                if (!isRecording) {
                    val ret = initMicrophone()
                    if (!ret) {
                        Logcat.d("Failed to initialize microphone")
                        statusBack(isRecording)
                    }
                    Logcat.d("state: ${audioRecord?.state}")

                    // 初始化音频增强
                    initAudioEnhancements()

                    audioRecord!!.startRecording()
                    isRecording = true
                    statusBack(isRecording)

                    recordingThread = thread(true) {
                        model.reset(true)
                        processSamples(callback)
                    }
                    Logcat.d("开始录音")
                } else {
                    isRecording = false
                    statusBack(isRecording)
                    audioRecord!!.stop()
                    audioRecord!!.release()
                    audioRecord = null
                    Logcat.d("停止录音")

                    // 复原 AudioManager 模式
                    audioManager.mode = previousAudioMode
                }
            } else {
                isRecording = false
                statusBack(isRecording)
                audioRecord?.stop()
                audioRecord?.release()
                audioRecord = null
                Logcat.d("录音被拒绝或结束时，同样复原 AudioManager 模式")

                // 录音被拒绝或结束时，同样复原 AudioManager 模式
                audioManager.mode = previousAudioMode
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun initMicrophone(): Boolean {
        val numBytes = AudioRecord.getMinBufferSize(sampleRateInHz, channelConfig, audioFormat)
        audioRecord = AudioRecord(
            audioSource,
            sampleRateInHz,
            channelConfig,
            audioFormat,
            numBytes * 4
        )
        return true
    }

    private fun processSamples(callback: (String) -> Unit) {
        val interval = 50 // i.e., 100 ms
        val bufferSize = (interval * sampleRateInHz / 1000.0).toInt() // in samples
        val buffer = ShortArray(bufferSize)
        while (isRecording) {
            val ret = audioRecord?.read(buffer, 0, buffer.size)
            if (ret != null && ret > 0) {
                val samples = FloatArray(ret) { buffer[it] / 32768.0f }
                model.acceptSamples(samples)
                while (model.isReady()) {
                    model.decode()
                }
                val isEndpoint = model.isEndpoint()
                val text = model.text


                if (isEndpoint) {
                    model.reset()
                    if (text.isNotBlank()) {
                        callback(text)
                    }
                }
            }
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            audioRecord?.apply {
                if (state == AudioRecord.STATE_INITIALIZED) {
                    stop()
                }
                release()
            }
            audioRecord = null

            // 释放音频增强组件
            noiseSuppressor?.release()
            acousticEchoCanceler?.release()
            automaticGainControl?.release()

            noiseSuppressor = null
            acousticEchoCanceler = null
            automaticGainControl = null

        } catch (e: Exception) {
            Logcat.e("清理资源失败", e)
        }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        try {
            isRecording = false
            cleanup()
            Logcat.d("SttHelper 资源已释放")
        } catch (e: Exception) {
            Logcat.e("释放资源失败", e)
        }
    }

}