# 🎵 STT音频打断功能实现

## 🎯 **功能概述**

为了解决STT录制时会录制到扬声器声音的问题，实现了一个智能的音频播放状态管理系统，通过打断按钮控制STT录制和音频播放的状态。

## 🔧 **核心功能**

### **1. 状态管理变量**
```kotlin
// 🎵 音频播放状态管理
private var isAudioPlaying = false // 是否正在播放音频
private var canProcessStt = true // 是否可以处理STT输入
private var pendingSttTexts = mutableListOf<String>() // 播放期间的待处理STT文本
```

### **2. 智能STT处理**
- **播放期间**: STT输入被忽略，避免录制到扬声器声音
- **播放完成**: 自动恢复STT处理功能
- **用户打断**: 立即停止播放并恢复STT处理

### **3. 专用打断按钮**
- **播放时**: 显示红色暂停按钮，点击可打断播放
- **空闲时**: 自动隐藏，不影响其他UI元素
- **独立功能**: 与滚动按钮分离，功能更加明确

## 🚀 **工作流程**

### **正常播放流程**
```
1. AI回复 → 音频加入队列
2. 播放开始 → isAudioPlaying=true, canProcessStt=false
3. STT输入 → 被忽略，记录日志
4. 播放完成 → isAudioPlaying=false, canProcessStt=true
5. STT恢复 → 可以正常处理语音输入
```

### **用户打断流程**
```
1. 播放中 → 用户点击打断按钮
2. 立即停止 → audioPlaybackUtils.stop()
3. 状态重置 → isAudioPlaying=false, canProcessStt=true
4. STT恢复 → 用户可以立即说话
```

## 📋 **关键方法说明**

### **handleSttTextWithDebounce()**
```kotlin
// 🎵 检查是否可以处理STT输入
if (!canProcessStt) {
    Logcat.d("STT 被阻止: 音频播放中，忽略文本 '$text'")
    pendingSttTexts.add(text)
    return
}
```

### **interruptAudioAndEnableStt()**
```kotlin
// 停止音频播放并清空队列
audioPlaybackUtils.stop()

// 更新状态
isAudioPlaying = false
canProcessStt = true

// 更新UI按钮状态
updateInterruptButtonState()
```

### **音频状态监听**
```kotlin
audioPlaybackUtils.setOnPlaybackStateChangedListener { state ->
    when (state) {
        AudioPlaybackState.PLAYING -> onAudioPlaybackStarted()
        AudioPlaybackState.IDLE -> onAudioPlaybackCompleted()
        AudioPlaybackState.ERROR -> onAudioPlaybackCompleted()
    }
}
```

### **按钮状态更新**
```kotlin
private fun updateInterruptButtonState() {
    runOnUiThread {
        if (isAudioPlaying) {
            // 播放中，显示打断按钮
            mBinding.interruptAudioFab.visibility = android.view.View.VISIBLE
            mBinding.interruptAudioFab.setImageResource(android.R.drawable.ic_media_pause)
        } else {
            // 没有播放，隐藏打断按钮
            mBinding.interruptAudioFab.visibility = android.view.View.GONE
        }
    }
}
```

### **按钮事件绑定**
```kotlin
// 🎵 音频打断按钮
mBinding.interruptAudioFab.singleClick {
    interruptAudioAndEnableStt()
}

// 滚动到底部按钮（独立功能）
mBinding.scrollToBottomFab.singleClick {
    BasicMarkdownRecyclerUtils.forceScrollToBottom(mBinding.recyclerView)
}
```

## 🎨 **UI交互设计**

### **新增布局元素**
```xml
<!-- 🎵 音频打断按钮 -->
<com.google.android.material.floatingactionbutton.FloatingActionButton
    android:id="@+id/interruptAudioFab"
    android:src="@android:drawable/ic_media_pause"
    android:contentDescription="打断音频播放"
    android:visibility="gone"
    app:layout_constraintBottom_toTopOf="@id/scrollToBottomFab"
    app:layout_constraintEnd_toEndOf="parent"
    app:fabSize="mini"
    app:backgroundTint="@android:color/holo_red_light" />
```

### **按钮布局设计**
- **打断按钮**: 🔴 红色暂停图标 (ic_media_pause)，播放时显示，空闲时隐藏
- **滚动按钮**: ⬇️ 蓝色滚动图标 (arrow_down_float)，独立控制显示/隐藏
- **位置关系**: 打断按钮在滚动按钮上方，避免冲突

### **用户体验优化**
- **即时响应**: 点击打断按钮立即生效
- **状态反馈**: 按钮图标实时反映当前状态
- **智能切换**: 播放完成自动恢复STT功能

## 🔍 **调试信息**

### **关键日志**
```kotlin
// STT被阻止时
"STT 被阻止: 音频播放中，忽略文本 '$text'"

// 用户主动打断
"🎵 用户主动打断音频播放"

// 播放状态变化
"🎵 音频播放开始，禁用STT处理"
"🎵 音频播放完成，启用STT处理"
```

### **状态跟踪**
- `isAudioPlaying`: 音频播放状态
- `canProcessStt`: STT处理权限
- `pendingSttTexts`: 播放期间的STT文本（用于调试）

## ✅ **优势特点**

### **🎯 解决核心问题**
- ✅ **避免回音**: 播放时禁用STT，防止录制扬声器声音
- ✅ **用户控制**: 提供打断按钮，用户可主动控制
- ✅ **自动恢复**: 播放完成自动恢复STT功能

### **🚀 用户体验**
- ✅ **即时响应**: 打断操作立即生效
- ✅ **状态清晰**: 按钮图标明确显示当前状态
- ✅ **智能切换**: 根据播放状态自动调整功能

### **🔧 技术实现**
- ✅ **状态管理**: 清晰的状态变量和转换逻辑
- ✅ **事件驱动**: 基于音频播放状态的事件响应
- ✅ **错误处理**: 播放错误时也能正确恢复STT

## 🛠️ **使用方式**

### **正常使用**
1. 用户说话 → STT识别 → AI回复 → 音频播放
2. 播放期间 → STT自动禁用，避免录制扬声器声音
3. 播放完成 → STT自动恢复，可以继续对话

### **主动打断**
1. 播放期间 → 点击打断按钮（暂停图标）
2. 立即停止播放 → STT恢复可用
3. 用户可以立即说话 → 开始新的对话轮次

## 🎯 **适用场景**

- **连续对话**: 避免AI回复时的语音干扰
- **快速打断**: 用户想要立即说话时
- **错误纠正**: AI回复错误时快速打断
- **紧急情况**: 需要立即停止播放的场景

这个实现完美解决了STT录制扬声器声音的问题，同时提供了良好的用户控制体验！
