package com.haoxue.pose

import android.content.Context
import com.haoxue.libcommon.audio.FastAudioPlayer
import com.lazy.library.logging.Logcat

/**
 * 智能音频管理器
 * 统一管理所有姿态检测相关的音频播放功能
 * 解决快速跳绳时音频播放延迟问题
 */
class SmartAudioManager {

    companion object {
        private const val AUDIO_BASE_PATH = "voice/"
        private const val AUDIO_EXTENSION = ".mp3"

        // 快速音频播放器
        private lateinit var fastAudioPlayer: FastAudioPlayer
        private var isInitialized = false

        // 跟踪上次播放的计数和时间
        private var lastPlayedCount = 0
        private var lastPlayTime = 0L

        // 播放状态跟踪
        private var lastPlayedAudio = ""
        private var lastAudioPlayTime = 0L

        // 播放完成回调
        private var onPlayFinishListener: ((path: String) -> Unit)? = null

        /**
         * 初始化音频管理器
         * @param context 上下文
         */
        fun initialize(context: Context) {
            if (isInitialized) {
                Logcat.w("SmartAudioManager 已经初始化")
                return
            }

            fastAudioPlayer = FastAudioPlayer(context)

            // 设置播放状态回调
            fastAudioPlayer.setOnPlaybackStateChanged { state ->
                when (state) {
                    FastAudioPlayer.FastAudioState.PLAYING -> {
                        // 可以在这里处理播放开始事件
                    }

                    FastAudioPlayer.FastAudioState.COMPLETED -> {
                        // 播放完成时触发回调
                        Logcat.d("音频播放完成: $lastPlayedAudio")
                    }

                    FastAudioPlayer.FastAudioState.STOPPED -> {
                        // 手动停止时的处理
                        Logcat.d("音频播放停止: $lastPlayedAudio")
                    }

                    else -> {}
                }
            }

            // 设置播放完成回调
            fastAudioPlayer.setOnPlaybackCompleted { audioPath ->
                onPlayFinishListener?.invoke(audioPath)
                Logcat.d("音频播放完成回调: $audioPath")
            }

            // 预加载所有音频
            preloadAllAudios()

            isInitialized = true
            Logcat.d("SmartAudioManager 初始化完成")
        }

        /**
         * 预加载所有音频文件
         */
        private fun preloadAllAudios() {
            val audioList = listOf(
                // 系统提示音
                getFullPath(PoseAudio.WELCOME),
                getFullPath(PoseAudio.READY),
                getFullPath(PoseAudio.START),
                getFullPath(PoseAudio.JUMP),
                getFullPath(PoseAudio.BEYOND_SCREEN),

                // 计数音频
                getFullPath(PoseAudio.COUNT_10),
                getFullPath(PoseAudio.COUNT_20),
                getFullPath(PoseAudio.COUNT_30),
                getFullPath(PoseAudio.COUNT_40),
                getFullPath(PoseAudio.COUNT_50),
                getFullPath(PoseAudio.COUNT_60),
                getFullPath(PoseAudio.COUNT_70),
                getFullPath(PoseAudio.COUNT_80),
                getFullPath(PoseAudio.COUNT_90),
                getFullPath(PoseAudio.COUNT_100),
                getFullPath(PoseAudio.COUNT_110),
                getFullPath(PoseAudio.COUNT_120),
                getFullPath(PoseAudio.COUNT_150),
                getFullPath(PoseAudio.COUNT_200),
                getFullPath(PoseAudio.COUNT_250),
                getFullPath(PoseAudio.COUNT_300),
                getFullPath(PoseAudio.COUNT_400),
                getFullPath(PoseAudio.COUNT_500),
                getFullPath(PoseAudio.COUNT_600),
                getFullPath(PoseAudio.COUNT_700),
                getFullPath(PoseAudio.COUNT_800)
            )

            fastAudioPlayer.preloadAudios(audioList)
            Logcat.d("开始预加载 ${audioList.size} 个音频文件")
        }

        /**
         * 播放指定音频
         * @param audio 音频枚举
         */
        fun play(audio: PoseAudio, need: Boolean = true) {
            if (!isInitialized) {
                Logcat.e("SmartAudioManager 未初始化")
                return
            }

            val audioPath = getFullPath(audio)
            val currentTime = System.currentTimeMillis()

            if (need && audioPath == lastPlayedAudio) return

            // 防止重复播放相同音频（100ms内）
            if (audioPath == lastPlayedAudio && currentTime - lastAudioPlayTime < 100) {
                return
            }

            // 特殊处理：某些音频需要清空当前播放
            val shouldInterrupt = when (audio) {
                PoseAudio.READY-> true
                else -> false
            }

            val success = fastAudioPlayer.playFast(audioPath, shouldInterrupt)
            if (success) {
                lastPlayedAudio = audioPath
                lastAudioPlayTime = currentTime
                Logcat.d("播放音频: ${audio.path}")
            } else {
                Logcat.w("播放音频失败: ${audio.path}")
            }
        }

        /**
         * 智能播放计数音频
         * 根据跳绳速度自动调整播放策略
         */
        fun smartPlayCount(count: Int) {
            val currentTime = System.currentTimeMillis()

            // 只在计数变化时处理
            if (count > lastPlayedCount) {
                handleAudioBySpeed(count)
                lastPlayedCount = count
                lastPlayTime = currentTime
            }
        }

        /**
         * 播放计数音频
         */
        fun playCount(num: Int) {
            val audio = when (num) {
                10 -> PoseAudio.COUNT_10
                20 -> PoseAudio.COUNT_20
                30 -> PoseAudio.COUNT_30
                40 -> PoseAudio.COUNT_40
                50 -> PoseAudio.COUNT_50
                60 -> PoseAudio.COUNT_60
                70 -> PoseAudio.COUNT_70
                80 -> PoseAudio.COUNT_80
                90 -> PoseAudio.COUNT_90
                100 -> PoseAudio.COUNT_100
                110 -> PoseAudio.COUNT_110
                120 -> PoseAudio.COUNT_120
                150 -> PoseAudio.COUNT_150
                200 -> PoseAudio.COUNT_200
                250 -> PoseAudio.COUNT_250
                300 -> PoseAudio.COUNT_300
                400 -> PoseAudio.COUNT_400
                500 -> PoseAudio.COUNT_500
                600 -> PoseAudio.COUNT_600
                700 -> PoseAudio.COUNT_700
                800 -> PoseAudio.COUNT_800
                else -> PoseAudio.JUMP
            }

            play(audio, false)
        }

        /**
         * 根据速度处理音频播放
         */
        private fun handleAudioBySpeed(count: Int) {
            playCount(count)
        }

        /**
         * 播放模式枚举
         */
        private enum class PlayMode {
            ALL,                // 播放所有
            IMPORTANT,          // 播放重要节点
            MILESTONES,         // 播放里程碑
            MAJOR_MILESTONES    // 播放大里程碑
        }

        /**
         * 停止当前播放
         */
        fun stop() {
            if (isInitialized) {
                fastAudioPlayer.stop()
                Logcat.d("停止音频播放")
            }
        }

        /**
         * 停止所有播放
         */
        fun stopAll() {
            if (isInitialized) {
                fastAudioPlayer.stopAll()
                Logcat.d("停止所有音频播放")
            }
        }

        /**
         * 暂停播放（兼容原有接口）
         */
        fun pause() {
            stop()
        }

        /**
         * 恢复播放（兼容原有接口）
         */
        fun resume() {
            // SoundPool不需要恢复操作，播放是即时的
        }

        /**
         * 清空队列（兼容原有接口）
         */
        fun clearQueue() {
            stop()
        }

        /**
         * 设置播放完成回调
         */
        fun setOnPlayFinishListener(listener: (path: String) -> Unit) {
            onPlayFinishListener = listener
        }

        /**
         * 检查音频是否已加载
         */
        fun isAudioLoaded(audio: PoseAudio): Boolean {
            return if (isInitialized) {
                fastAudioPlayer.isAudioLoaded(getFullPath(audio))
            } else {
                false
            }
        }

        /**
         * 获取已加载的音频数量
         */
        fun getLoadedAudioCount(): Int {
            return if (isInitialized) {
                fastAudioPlayer.getLoadedAudioCount()
            } else {
                0
            }
        }

        /**
         * 重置状态
         */
        fun reset() {
            lastPlayedCount = 0
            lastPlayTime = 0L
            lastPlayedAudio = ""
            lastAudioPlayTime = 0L
            Logcat.d("SmartAudioManager 状态已重置")
        }

        /**
         * 释放资源
         */
        fun release() {
            if (isInitialized) {
                fastAudioPlayer.release()
                isInitialized = false
                Logcat.d("SmartAudioManager 资源已释放")
            }
        }

        /**
         * 获取完整音频路径
         */
        fun getFullPath(audio: PoseAudio): String {
            return "${AUDIO_BASE_PATH}${audio.path}${AUDIO_EXTENSION}"
        }
    }

    /**
     * 姿态检测相关音频枚举
     */
    enum class PoseAudio(val path: String) {
        // 系统提示音
        WELCOME("放好手机退后两步直到能在屏幕里看到全身"),
        READY("开始跳绳吧正在给你计数喔"),
        START("倒计时321开始"),
        JUMP("每次跳绳音效"),
        BEYOND_SCREEN("请保持全身在屏幕内"),

        // 计数提示
        COUNT_10("跳绳播报10"),
        COUNT_20("跳绳播报20"),
        COUNT_30("跳绳播报30"),
        COUNT_40("跳绳播报40"),
        COUNT_50("跳绳播报50"),
        COUNT_60("跳绳播报60"),
        COUNT_70("跳绳播报70"),
        COUNT_80("跳绳播报80"),
        COUNT_90("跳绳播报90"),
        COUNT_100("跳绳播报100"),
        COUNT_110("跳绳播报110"),
        COUNT_120("跳绳播报120"),
        COUNT_150("跳绳播报150"),
        COUNT_200("跳绳播报200"),
        COUNT_250("跳绳播报250"),
        COUNT_300("跳绳播报300"),
        COUNT_400("跳绳播报400"),
        COUNT_500("跳绳播报500"),
        COUNT_600("跳绳播报600"),
        COUNT_700("跳绳播报700"),
        COUNT_800("跳绳播报800"),
    }
}
