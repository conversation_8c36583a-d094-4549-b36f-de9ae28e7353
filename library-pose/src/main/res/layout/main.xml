<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <Spinner
                android:id="@+id/spinnerModel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawSelectorOnTop="true"
                android:entries="@array/model_array" />

            <Spinner
                android:id="@+id/spinnerCPUGPU"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawSelectorOnTop="true"
                android:entries="@array/cpugpu_array" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <SurfaceView
                android:id="@+id/cameraview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="280dp"
                android:layout_height="540dp"
                android:scaleType="fitXY"
                android:src="@drawable/camrea_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="240dp"
                android:layout_height="460dp"
                android:scaleType="fitXY"
                android:src="@drawable/camera_user"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/numGo"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#80000000"
                android:gravity="center"
                android:text="GO"
                android:textColor="@color/common_color_white"
                android:textSize="140dp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="24dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/time"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:layout_width="120dp"
                    android:layout_height="50dp"
                    android:background="@drawable/pose_num_round_bg"
                    android:text="22:59"
                    android:textColor="@color/common_color_white"
                    android:textSize="34dp" />

                <Space
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"/>

                <LinearLayout
                    android:gravity="right"
                    android:id="@+id/jumpCountersLayout"
                    android:layout_width="120dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent">

                <Button
                    android:id="@+id/buttonToggleDraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:text="显示调试" />

                <Button
                    android:id="@+id/buttonSwitchCamera"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:text="切换摄像头" />

                <Button
                    android:id="@+id/buttonResetCounter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="重置检测"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>
