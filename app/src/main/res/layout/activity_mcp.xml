<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp"
        tools:context=".McpActivity">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            android:orientation="vertical"
            android:layout_marginBottom="60dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:padding="4dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <Button
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/add"
            android:textSize="17dp"
            android:textStyle="bold"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="开始" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/scrollToBottomFab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:src="@android:drawable/arrow_down_float"
            android:contentDescription="滚动到底部"
            android:visibility="gone"
            android:tint="@color/black"
            app:backgroundTint="@color/theme_color"
            app:layout_constraintBottom_toTopOf="@id/interruptAudioFab"
            app:layout_constraintEnd_toEndOf="parent"
            app:fabSize="mini" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/interruptAudioFab"
            android:layout_width="wrap_content"
            android:tint="@color/black"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:visibility="gone"
            android:src="@android:drawable/ic_media_pause"
            android:contentDescription="打断音频播放"
            app:layout_constraintBottom_toTopOf="@id/add"
            app:layout_constraintEnd_toEndOf="parent"
            app:fabSize="mini"
            app:backgroundTint="@color/theme_color" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>