package com.haoxue.sportai

import androidx.lifecycle.lifecycleScope
import com.haoxue.libcommon.ConstData
import com.haoxue.libcommon.audio.AudioQueuePlayer
import com.haoxue.libcommon.markdown.BasicChatAdapter
import com.haoxue.libcommon.markdown.BasicMarkdownRecyclerUtils
import com.haoxue.libcommon.markdown.BasicMarkdownRenderer
import com.haoxue.libcommon.markdown.BasicMarkdownUtils
import com.haoxue.libcommon.singleClick
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.libcommon.utils.AudioPlaybackUtils
import com.haoxue.libcommon.utils.SafeWebSocketUtils
import com.haoxue.libcommon.utils.SseUtils
import com.haoxue.mcpserver.McpServerHelper
import com.haoxue.mcpserver.ToolsUtils
import com.haoxue.sportai.databinding.ActivityMcpBinding
import com.haoxue.stt.SttHelper
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

class McpActivity : BaseActivity<ActivityMcpBinding>(R.layout.activity_mcp) {
    // 使用基础的聊天适配器
    private lateinit var chatAdapter: BasicChatAdapter

    // Markdown 相关
    private val markdownBuilder = StringBuilder()
    private var isStreamingMarkdown = false

    // STT 防抖和文本拼接相关
    private var sttDebounceJob: Job? = null
    private val sttTextBuilder = StringBuilder()
    private var lastSttText = ""
    private val sttDebounceDelayMs = 2000L // 防抖延迟

    // 当前用户消息的索引（用于实时更新）
    private var currentUserMessageIndex = -1

    // 当前AI消息的索引（用于SSE请求管理）
    private var currentAiMessageIndex = -1

    // SSE 请求管理
    private var currentSseJob: Job? = null

    // 🎵 音频播放状态管理
    private var isAudioPlaying = false // 是否正在播放音频

    private lateinit var audioPlaybackUtils: AudioPlaybackUtils

    override fun initCommonData() {
        mBinding.add.singleClick {
            SttHelper.startRecord(this, { isRecording ->
                mBinding.add.text = if (isRecording) "结束" else "开始"
            }) { recognizedText ->
                Logcat.d("stt---${recognizedText}")
                // 使用防抖机制处理STT文本
                handleSttTextWithDebounce(recognizedText)
            }
        }

        // 🎵 音频打断按钮
        mBinding.interruptAudioFab.singleClick {
            interruptAudioAndEnableStt()
        }

        // 滚动到底部按钮
        mBinding.scrollToBottomFab.singleClick {
            BasicMarkdownRecyclerUtils.forceScrollToBottom(mBinding.recyclerView)
        }

        mBinding.scrollToBottomFab.setOnLongClickListener {
            SafeWebSocketUtils.manualReconnect()
            Logcat.d("手动触发WebSocket重连")
            true
        }
    }

    /**
     * STT 文本处理 - 立即显示，延迟请求
     * @param text 识别到的文本
     */
    private fun handleSttTextWithDebounce(text: String) {
        if (text.isBlank()) return

        // 🎵 检查STT是否正在活跃录制（未暂停）
        if (!SttHelper.isActivelyRecording()) {
            Logcat.d("STT 被暂停: 音频播放中，忽略文本 '$text'")
            return
        }

        // 如果识别到新的文本片段，立即打断当前 TTS 播放并清空队列，准备后续新语音
        if (text.isNotBlank()) {
            runOnUiThread {
                audioPlaybackUtils.stop()
            }
        }

        // 取消之前的防抖任务
        sttDebounceJob?.cancel()

        // 🚫 取消当前正在进行的SSE请求
        currentSseJob?.cancel()
        currentSseJob = null
        Logcat.d("STT 新输入: 取消当前SSE请求")

        // 直接拼接文本（不做重复判断）
        appendSttText(text)

        // 立即显示当前拼接的文本
        runOnUiThread {
            var displayCurrentSttText = displayCurrentSttText()
            if (displayCurrentSttText !== null) {
                // 清空文本构建器，准备下一轮
                sttTextBuilder.clear()
                lastSttText = ""
                currentUserMessageIndex = -1
                return@runOnUiThread
            }
        }

        Logcat.d("STT 实时: 收到文本 '$text'，当前拼接: '${sttTextBuilder.toString().trim()}'")
        lastSttText = text

        // 重启防抖任务（每次新文本都重新计时）
        sttDebounceJob = lifecycleScope.launch {
            try {
                delay(sttDebounceDelayMs)

                // 防抖时间到，处理拼接的完整文本
                val completeText = sttTextBuilder.toString().trim()
                if (completeText.isNotBlank()) {
//                    Logcat.d("STT 防抖完成: 处理完整文本 '$completeText'")

                    // 最终更新用户消息并请求AI回复
                    finalizeUserMessage(completeText)

                    // 清空文本构建器，准备下一轮
                    sttTextBuilder.clear()
                    lastSttText = ""
                    currentUserMessageIndex = -1
                } else {
                    Logcat.d("STT 防抖完成: 文本为空，跳过处理")
                }
            } catch (e: CancellationException) {
                Logcat.d("STT 防抖: 任务被取消（收到新文本）")
            } catch (e: Exception) {
                Logcat.e("STT 防抖处理异常", e)
            }
        }
    }

    /**
     * 立即显示当前STT文本
     */
    private fun displayCurrentSttText(): String? {
        val currentText = sttTextBuilder.toString().trim()
        if (currentText.isBlank()) return null

        if (currentUserMessageIndex == -1) {
            // 第一次显示，创建新的用户消息
            val userMessage = BasicChatAdapter.ChatMessage(
                type = 0, // 用户消息
                content = currentText,
                isMarkdown = false
            )
            chatAdapter.addMessage(userMessage)
            currentUserMessageIndex = chatAdapter.itemCount - 1

            Logcat.d("STT 实时显示: 创建新消息 '$currentText'")
        } else {
            // 更新现有的用户消息
            chatAdapter.updateMessage(currentUserMessageIndex, currentText)

            Logcat.d("STT 实时更新: 更新消息 '$currentText'")
        }

        // 滚动到底部显示最新内容
        BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)

        // 检查本地指令拦截
        val result = ToolsUtils.interceptFun(currentText)
        if (result != null) {
            // 如果匹配本地指令，直接返回结果，不调用AI
            val aiMessage = BasicChatAdapter.ChatMessage(
                type = 1, // AI消息
                content = result,
                isMarkdown = false
            )
            chatAdapter.addMessage(aiMessage)
            BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
        }
        return result
    }

    /**
     * 完成用户消息并请求AI回复
     */
    private fun finalizeUserMessage(completeText: String) {
        Logcat.d("STT 完成: 最终文本 '$completeText'")

        // 确保用户消息已显示
        if (currentUserMessageIndex == -1) {
            val userMessage = BasicChatAdapter.ChatMessage(
                type = 0, // 用户消息
                content = completeText,
                isMarkdown = false
            )
            chatAdapter.addMessage(userMessage)
        } else {
            // 最终更新用户消息
            chatAdapter.updateMessage(currentUserMessageIndex, completeText)
        }
        // 请求AI回复
        callSseExample(question = completeText)
    }

    /**
     * 直接拼接 STT 文本
     */
    private fun appendSttText(newText: String) {
        if (sttTextBuilder.isNotEmpty()) {
            // 如果已有文本，添加空格分隔
            sttTextBuilder.append(" ")
        }
        sttTextBuilder.append(newText)
        Logcat.d("STT 拼接: 添加文本 '$newText'，当前完整文本: '${sttTextBuilder.toString()}'")
    }

    /**
     * 🎵 打断音频播放并启用STT处理
     */
    private fun interruptAudioAndEnableStt() {
        Logcat.d("🎵 用户主动打断音频播放")

        // 停止音频播放并清空队列
        audioPlaybackUtils.stop()

        // 更新状态
        isAudioPlaying = false
        SttHelper.resumeRecording()

        // 更新UI按钮状态
        updateInterruptButtonState()

        Logcat.d("🎵 音频已打断，STT录制已恢复")
    }

    /**
     * 🎵 更新打断按钮的状态和显示
     */
    private fun updateInterruptButtonState() {
        runOnUiThread {
            if (isAudioPlaying) {
                // 播放中，显示打断按钮
                mBinding.interruptAudioFab.visibility = android.view.View.VISIBLE
                mBinding.interruptAudioFab.setImageResource(android.R.drawable.ic_media_pause)
                Logcat.d("🎵 显示音频打断按钮")
            } else {
                // 没有播放，隐藏打断按钮
                mBinding.interruptAudioFab.visibility = android.view.View.GONE
                Logcat.d("🎵 隐藏音频打断按钮")
            }
        }
    }

    /**
     * 🎵 音频播放开始时的处理
     */
    private fun onAudioPlaybackStarted() {
        Logcat.d("🎵 音频播放开始，暂停STT录制")
        isAudioPlaying = true
        SttHelper.pauseRecording()
        updateInterruptButtonState()
    }

    /**
     * 🎵 检查是否所有音频播放真正完成
     */
    private fun checkAllAudioPlaybackCompleted() {
        val queueSize = audioPlaybackUtils.getQueueSize()
        val currentState = audioPlaybackUtils.getCurrentState()

        Logcat.d("🎵 检查所有音频播放完成状态:")
        Logcat.d("   - 队列长度: $queueSize")
        Logcat.d("   - 播放状态: $currentState")
        Logcat.d("   - 当前播放标志: $isAudioPlaying")

        // 只有在队列真正为空且播放器空闲时才启用STT
        if (queueSize == 0 && currentState == AudioQueuePlayer.AudioPlaybackState.IDLE && isAudioPlaying) {
            Logcat.d("🎵 ✅ 所有音频播放完成，启用STT处理")
            onAudioPlaybackCompleted()
        } else {
            Logcat.d("🎵 ⏳ 还有音频在队列中或正在播放，继续等待")
        }
    }

    /**
     * 🎵 音频播放完成时的处理
     */
    private fun onAudioPlaybackCompleted() {
        Logcat.d("🎵 所有音频播放完成，恢复STT录制")
        isAudioPlaying = false
        SttHelper.resumeRecording()
        updateInterruptButtonState()
    }


    /**
     * SSE 调用示例 - 使用优化的 Markwon RecyclerView 适配器
     */
    private fun callSseExample(question: String = "") {
        // 🚫 取消之前的SSE请求
        currentSseJob?.cancel()

        // 重置 Markdown 构建器
        markdownBuilder.clear()
        isStreamingMarkdown = true

        // 添加 AI 回复的占位符
        val aiMessage = BasicChatAdapter.ChatMessage(
            type = 1, // AI 消息
            content = "",
            isMarkdown = false
        )
        chatAdapter.addMessage(aiMessage)

        // 🎯 保存当前SSE请求Job，以便后续可以取消
        currentSseJob = SseUtils.quickAsk(
            lifecycleScope,
            question,
            onComplete = { completeResponse ->
                runOnUiThread {
                    Logcat.d("SSE 完整回答: $completeResponse")
                    isStreamingMarkdown = false
                    currentSseJob = null // 请求完成，清空Job引用

                    // 最终更新完整的 Markdown 内容
                    val isMarkdown = BasicMarkdownUtils.isMarkdown(completeResponse)
                    chatAdapter.updateLastMessage(completeResponse, isMarkdown)

                    // 自动滚动到底部
                    BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
                }
            },
            onError = { error ->
                runOnUiThread {
                    isStreamingMarkdown = false
                    currentSseJob = null // 请求出错，清空Job引用
                    chatAdapter.updateLastMessage("❌ 请求失败: $error", false)
                    Logcat.e("SSE 请求失败: $error")
                }
            },
            onMessage = { content ->
                runOnUiThread {
                    // 流式更新 Markdown 内容
                    markdownBuilder.append(content)

                    val currentContent = markdownBuilder.toString()
                    val isMarkdown = BasicMarkdownUtils.isMarkdown(currentContent)

                    // 更新最后一条消息
                    chatAdapter.updateLastMessage(currentContent, isMarkdown)

                    // 自动滚动到底部
                    BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
                }
            }
        )
    }


    override fun initCommonListener() {
        SttHelper.init(this)
        McpServerHelper.init(this)

        // 🎵 初始化音频播放工具
        audioPlaybackUtils = AudioPlaybackUtils()
        audioPlaybackUtils.initialize(this@McpActivity)

        // 初始化基础版 Markdown 渲染器
        BasicMarkdownRenderer.initialize(this)

        // 配置WebSocket自动重连
        SafeWebSocketUtils.configureReconnect(
            enabled = true,
            maxAttempts = -1,      // 无限重连，直到连接成功
            intervalMs = 2000L,    // 基础间隔2秒
            maxIntervalMs = 10000L // 最大间隔30秒
        )

        // 设置基础聊天适配器
        chatAdapter = BasicMarkdownRecyclerUtils.setupChatRecyclerView(
            mBinding.recyclerView,
            this,
            onItemClick = { message, position ->
                // 点击消息的处理
                Logcat.d("点击消息: ${message.content}")
            },
            onScrollStateChanged = { shouldShowScrollButton ->
                // 控制滚动到底部按钮的显示/隐藏
                runOnUiThread {
                    mBinding.scrollToBottomFab.visibility = if (shouldShowScrollButton) {
                        android.view.View.VISIBLE
                    } else {
                        android.view.View.GONE
                    }
                }
            }
        )
        SafeWebSocketUtils.safeConnect(
            lifecycleScope,
            ConstData.TTS_WEBSOCKET_URL,
            onMessage = { message ->
                Logcat.d("WebSocket 收到音频消息: $message")
                audioPlaybackUtils.playAudio(message)
            },
            onConnected = {
                Logcat.d("🔗 WebSocket 音频连接成功")
                Logcat.d("📊 ${SafeWebSocketUtils.getReconnectInfo()}")
            },
            onDisconnected = {
                Logcat.d("❌ WebSocket 音频连接断开")
                // 连接断开时会自动尝试重连（如果启用）
            },
            onError = { error ->
                Logcat.e("🚨 WebSocket 音频连接错误: $error")
                // 错误时也会自动尝试重连（如果启用）
            }
        )

        // 设置音频播放队列变化监听器
        audioPlaybackUtils.setOnQueueChangedListener { queueSize ->
            Logcat.d("音频队列长度变化: $queueSize")

            // 🎵 当队列有内容时，表示开始播放
            if (queueSize > 0 && !isAudioPlaying) {
                onAudioPlaybackStarted()
            }

            // 🎵 当队列为空时，检查是否所有音频播放完成
            if (queueSize == 0) {
                // 延迟一点检查，确保播放状态已更新
                lifecycleScope.launch {
                    delay(100) // 短暂延迟确保状态同步
                    checkAllAudioPlaybackCompleted()
                }
            }
        }

        // 设置音频播放状态监听器
        audioPlaybackUtils.setOnPlaybackStateChangedListener { state ->
            Logcat.d("音频播放状态变化: $state")

            when (state) {
                AudioQueuePlayer.AudioPlaybackState.PLAYING -> {
                    // 🎵 播放开始
                    if (!isAudioPlaying) {
                        onAudioPlaybackStarted()
                    }
                }

                AudioQueuePlayer.AudioPlaybackState.IDLE -> {
                    // 🎵 播放器空闲时，检查是否所有音频播放完成
                    checkAllAudioPlaybackCompleted()
                }

                AudioQueuePlayer.AudioPlaybackState.ERROR -> {
                    // 🎵 播放错误，也视为完成
                    if (isAudioPlaying) {
                        Logcat.d("🎵 播放错误，视为播放完成")
                        onAudioPlaybackCompleted()
                    }
                }

                AudioQueuePlayer.AudioPlaybackState.COMPLETED -> {
                    // 🎵 单个音频播放完成，但不立即触发完成回调
                    // 等待队列检查或IDLE状态来确定是否真的全部完成
                    Logcat.d("🎵 单个音频播放完成，等待队列状态确认")
                }

                else -> {
                    // 其他状态暂不处理
                    Logcat.d("🎵 其他播放状态: $state")
                }
            }
        }

        // 设置音频播放错误监听器
        audioPlaybackUtils.setOnPlaybackErrorListener { error, exception ->
            Logcat.e("音频播放错误: $error", exception)
            // 🎵 播放错误时也要恢复STT处理
            if (isAudioPlaying) {
                onAudioPlaybackCompleted()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 取消 STT 防抖任务并清空文本构建器
        sttDebounceJob?.cancel()
        sttTextBuilder.clear()

        // 清理 RecyclerView 滚动监听器
        BasicMarkdownRecyclerUtils.cleanup(mBinding.recyclerView)

        SttHelper.release()
        McpServerHelper.stopServer()
        audioPlaybackUtils.release()
    }

    override fun requestCommonData() {
    }
}