package com.haoxue.sportai

import com.lazy.library.logging.Logcat

/**
 * STT状态管理器
 * 用于管理STT在不同页面的行为状态
 */
object SttStateManager {
    
    /**
     * STT工作模式
     */
    enum class SttMode {
        FULL_MODE,      // 完整模式：本地工具拦截 + SSE接口调用
        LOCAL_ONLY      // 仅本地模式：只执行本地工具拦截，不调用SSE接口
    }
    
    // 当前STT工作模式
    private var currentMode: SttMode = SttMode.FULL_MODE
    
    // 当前活跃的Activity名称
    private var currentActivity: String = ""
    
    /**
     * 设置STT为完整模式（在McpActivity中使用）
     */
    fun setFullMode(activityName: String = "McpActivity") {
        currentMode = SttMode.FULL_MODE
        currentActivity = activityName
        Logcat.d("📱 STT状态: 切换到完整模式 ($activityName)")
    }
    
    /**
     * 设置STT为仅本地模式（在其他Activity中使用）
     */
    fun setLocalOnlyMode(activityName: String = "OtherActivity") {
        currentMode = SttMode.LOCAL_ONLY
        currentActivity = activityName
        Logcat.d("📱 STT状态: 切换到仅本地模式 ($activityName)")
    }
    
    /**
     * 获取当前STT模式
     */
    fun getCurrentMode(): SttMode {
        return currentMode
    }
    
    /**
     * 检查是否应该调用SSE接口
     */
    fun shouldCallSse(): Boolean {
        val shouldCall = currentMode == SttMode.FULL_MODE
        Logcat.d("📱 STT状态检查: 当前模式=$currentMode, 活跃页面=$currentActivity, 是否调用SSE=$shouldCall")
        return shouldCall
    }
    
    /**
     * 检查是否应该执行本地工具拦截
     */
    fun shouldExecuteLocalTools(): Boolean {
        // 无论在哪种模式下，都应该执行本地工具拦截
        return true
    }
    
    /**
     * 获取当前活跃的Activity名称
     */
    fun getCurrentActivity(): String {
        return currentActivity
    }
    
    /**
     * 获取状态描述
     */
    fun getStatusDescription(): String {
        return "STT模式: $currentMode, 活跃页面: $currentActivity"
    }
}
