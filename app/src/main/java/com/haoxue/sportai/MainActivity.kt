package com.haoxue.sportai

import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.drake.channel.receiveEvent
import com.drake.channel.sendEvent
import com.haoxue.libcommon.router.RouterPaths
import com.haoxue.libcommon.singleClick
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.libcommon.utils.Toaster
import com.haoxue.sportai.databinding.ActivityMainBinding
import com.lazy.library.logging.Logcat

@Route(path = RouterPaths.App.MAIN)
class MainActivity : BaseActivity<ActivityMainBinding>(R.layout.activity_main) {
    override fun initCommonData() {
        val user = User()
        user.name = "123131231231231"
        mBinding.user = user

        Logcat.d("1231231231")

        mBinding.add.singleClick {
            sendEvent("你好啊")
        }

        receiveEvent<String> {
            Toaster.toast("你小子$it")
        }

        // ARouter 路由跳转示例
        // 注意：需要在布局文件中添加对应的按钮
        // 或者可以在现有按钮点击事件中测试路由跳转

    }

    override fun initCommonListener() {
    }

    override fun requestCommonData() {
    }

    fun tips(view: View) {
        Toaster.toast("1111")
    }

    override fun onResume() {
        super.onResume()
        // 设置STT为仅本地模式
        SttStateManager.setLocalOnlyMode("MainActivity")
    }
}